from rest_framework import viewsets, status, permissions
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db import transaction, models
from django.utils import timezone
from django.shortcuts import get_object_or_404
from django_tenants.utils import schema_context
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from tenants.models import Tenant
from workflows.models import CitizenTransferRequest, CitizenTransferHistory
from tenants.models.citizen import Citizen
from workflows.serializers import CitizenTransferRequestSerializer, CitizenTransferRequestReviewSerializer
# TODO: Create additional serializers as needed
# from tenants.serializers.transfer_serializers import (
#     CitizenTransferRequestCreateSerializer,
#     CitizenTransferRequestReviewSerializer,
#     CitizenTransferRequestCompleteSerializer,
#     CitizenTransferHistorySerializer,
#     TransferStatsSerializer,
#     KebeleTransferSummarySerializer
# )
from common.permissions import CanManageCitizens
from rest_framework.permissions import BasePermission
import json


class CanManageTransfers(BasePermission):
    """
    Permission class for managing citizen transfers.
    Kebele leaders and kebele admins can create, review, and complete transfers.
    Clerks can view transfers but not create them.
    """
    def has_permission(self, request, view):
        user = request.user

        # Debug logging
        print(f"🔍 CanManageTransfers permission check:")
        print(f"  User: {user}")
        print(f"  Is authenticated: {user.is_authenticated if user else False}")
        print(f"  User role: {getattr(user, 'role', 'NO_ROLE')}")
        print(f"  Request method: {request.method}")
        print(f"  Has tenant: {hasattr(user, 'tenant') if user else False}")
        if hasattr(user, 'tenant'):
            print(f"  Tenant: {user.tenant}")
            print(f"  Tenant type: {getattr(user.tenant, 'type', 'NO_TYPE')}")

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            print("❌ User not authenticated")
            return False

        # Superusers have full permission
        if user.is_superuser:
            print("✅ Superuser access granted")
            return True

        # Check if user has appropriate role
        allowed_roles = ['superadmin', 'city_admin', 'subcity_admin', 'kebele_admin', 'kebele_leader', 'clerk']
        if not hasattr(user, 'role') or user.role not in allowed_roles:
            print(f"❌ User role '{getattr(user, 'role', 'NO_ROLE')}' not in allowed roles: {allowed_roles}")
            return False

        # For safe methods (GET, HEAD, OPTIONS), all allowed roles have permission
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            print(f"✅ Safe method '{request.method}' access granted for role '{user.role}'")
            return True

        # For non-safe methods (POST, PUT, DELETE), only kebele leaders and admins can perform actions
        # Clerks can only view transfers, not create/modify them
        if user.role in ['kebele_leader', 'kebele_admin', 'superadmin', 'city_admin', 'subcity_admin']:
            print(f"✅ Non-safe method '{request.method}' access granted for role '{user.role}'")
            return True

        print(f"❌ Non-safe method '{request.method}' access denied for role '{user.role}'")
        return False


@method_decorator(csrf_exempt, name='dispatch')
class CitizenTransferViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing citizen transfer requests.

    Workflow:
    1. POST /transfers/ - Kebele A leader creates transfer request
    2. GET /transfers/ - List transfers (filtered by user's role and kebele)
    3. POST /transfers/{id}/review/ - Kebele B leader accepts/rejects
    4. POST /transfers/{id}/complete/ - Kebele A leader completes transfer
    5. POST /transfers/{id}/cancel/ - Cancel transfer request
    """

    serializer_class = CitizenTransferRequestSerializer
    # TEMPORARY: Disable permissions but keep authentication for schema context
    permission_classes = []



    def dispatch(self, request, *args, **kwargs):
        """Override dispatch to debug if ViewSet is reached."""
        print(f"🚀 CitizenTransferViewSet.dispatch called!")
        print(f"  Method: {request.method}")
        print(f"  Path: {request.path}")
        print(f"  User: {getattr(request, 'user', 'NO_USER')}")
        return super().dispatch(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        """Override create to add detailed error logging."""
        print(f"🔍 CREATE method called:")
        print(f"  Request data: {request.data}")
        print(f"  User: {request.user}")
        print(f"  User authenticated: {request.user.is_authenticated if request.user else False}")

        try:
            # Check serializer validation first
            serializer = self.get_serializer(data=request.data)
            print(f"🔍 Serializer created: {serializer}")

            if not serializer.is_valid():
                print(f"❌ Serializer validation failed:")
                print(f"  Errors: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            print(f"✅ Serializer validation passed")
            return super().create(request, *args, **kwargs)
        except Exception as e:
            print(f"❌ CREATE method error: {e}")
            print(f"❌ Error type: {type(e)}")
            import traceback
            print(f"❌ Traceback: {traceback.format_exc()}")
            raise

    def dispatch(self, request, *args, **kwargs):
        """Override dispatch to add debugging."""
        print(f"🚀 CitizenTransferViewSet.dispatch called!")
        print(f"  Method: {request.method}")
        print(f"  Path: {request.path}")
        print(f"  User: {request.user}")
        print(f"  User authenticated: {request.user.is_authenticated if request.user else False}")
        print(f"  User role: {getattr(request.user, 'role', 'NO_ROLE')}")

        return super().dispatch(request, *args, **kwargs)

    def check_permissions(self, request):
        """Override to add debugging for permission checks."""
        print(f"🔍 check_permissions called for {request.method} {request.path}")
        print(f"  User: {request.user}")
        print(f"  Permission classes: {self.permission_classes}")

        # Manually populate user.tenant from JWT token
        self._populate_user_tenant(request)

        try:
            result = super().check_permissions(request)
            print(f"✅ check_permissions passed")
            return result
        except Exception as e:
            print(f"❌ check_permissions failed: {e}")
            raise

    def _populate_user_tenant(self, request):
        """Manually populate user.tenant from JWT token."""
        try:
            print(f"🔍 _populate_user_tenant called:")
            print(f"  User: {request.user}")
            print(f"  User authenticated: {request.user.is_authenticated if request.user else False}")
            print(f"  User type: {type(request.user)}")

            if hasattr(request.user, 'tenant'):
                print(f"  User already has tenant: {request.user.tenant}")
                return  # Already populated

            # Extract tenant ID from JWT token
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]

                import jwt
                # Decode without verification to get tenant_id
                decoded = jwt.decode(token, options={"verify_signature": False})
                tenant_id = decoded.get('tenant_id')

                if tenant_id:
                    from tenants.models import Tenant
                    # Use public schema to get tenant info
                    from django.db import connections
                    connection = connections['default']
                    connection.set_schema_to_public()

                    tenant = Tenant.objects.get(id=tenant_id)
                    request.user.tenant = tenant
                    print(f"✅ Populated user.tenant: {tenant}")

        except Exception as e:
            print(f"❌ Error populating user.tenant: {e}")

    def get_queryset(self):
        """Get transfer requests based on user's role and kebele."""
        user = self.request.user

        print(f"🔍 get_queryset called:")
        print(f"  User: {user}")
        print(f"  User authenticated: {user.is_authenticated if user else False}")
        print(f"  Has tenant: {hasattr(user, 'tenant') if user else False}")
        if hasattr(user, 'tenant'):
            print(f"  Tenant: {user.tenant}")
            print(f"  Tenant type: {getattr(user.tenant, 'type', 'NO_TYPE')}")

        # Try to populate user.tenant if missing
        if not hasattr(user, 'tenant'):
            print("  🔧 Attempting to populate user.tenant...")
            self._populate_user_tenant(self.request)
            if hasattr(user, 'tenant'):
                print(f"  ✅ Successfully populated tenant: {user.tenant}")
            else:
                print("  ❌ Failed to populate tenant")

        if not hasattr(user, 'tenant') or user.tenant.type != 'kebele':
            print(f"  ❌ Returning empty queryset - no tenant or not kebele")
            return CitizenTransferRequest.objects.none()

        # Get transfers where user's kebele is involved
        queryset = CitizenTransferRequest.objects.filter(
            models.Q(source_kebele=user.tenant) |
            models.Q(destination_kebele=user.tenant)
        ).select_related(
            'source_kebele', 'destination_kebele',
            'requested_by', 'reviewed_by', 'completed_by'
        )

        # Filter by status if provided
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by direction (incoming/outgoing)
        direction = self.request.query_params.get('direction')
        if direction == 'outgoing':
            queryset = queryset.filter(source_kebele=user.tenant)
        elif direction == 'incoming':
            queryset = queryset.filter(destination_kebele=user.tenant)

        print(f"  ✅ Returning queryset with {queryset.count()} transfers")
        return queryset.order_by('-created_at')

    def list(self, request, *args, **kwargs):
        """Custom list method with explicit schema context."""
        print(f"🔍 Custom list method called:")
        print(f"  User: {request.user}")
        print(f"  User authenticated: {request.user.is_authenticated if request.user else False}")
        print(f"  Has tenant: {hasattr(request.user, 'tenant') if request.user else False}")

        # Manually populate user.tenant if missing
        if not hasattr(request.user, 'tenant'):
            print("  🔧 Populating user.tenant in list method...")
            self._populate_user_tenant(request)

        if hasattr(request.user, 'tenant'):
            print(f"  ✅ User tenant: {request.user.tenant}")
            print(f"  ✅ User tenant schema: {request.user.tenant.schema_name}")

            # Ensure we're in the correct schema context
            from django_tenants.utils import schema_context
            with schema_context(request.user.tenant.schema_name):
                print(f"  ✅ Executing in schema: {request.user.tenant.schema_name}")
                return super().list(request, *args, **kwargs)
        else:
            print("  ❌ No tenant found, cannot execute list")
            from rest_framework.response import Response
            return Response({'error': 'No tenant context available'}, status=400)

        # Call the default list implementation
        return super().list(request, *args, **kwargs)

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'review':
            return CitizenTransferRequestReviewSerializer
        return CitizenTransferRequestSerializer

    def perform_create(self, serializer):
        """Create a new transfer request."""
        user = self.request.user
        request_data = self.request.data

        print(f"🔍 perform_create called:")
        print(f"  User: {user}")
        print(f"  User authenticated: {user.is_authenticated if user else False}")
        print(f"  User role: {getattr(user, 'role', 'NO_ROLE')}")
        print(f"  Has tenant: {hasattr(user, 'tenant')}")
        print(f"  Request data: {request_data}")
        if hasattr(user, 'tenant'):
            print(f"  Tenant: {user.tenant}")
            print(f"  Tenant type: {getattr(user.tenant, 'type', 'NO_TYPE')}")

        if not user or not user.is_authenticated:
            print("❌ User not authenticated")
            raise PermissionError("Authentication required")

        if not hasattr(user, 'tenant') or user.tenant.type != 'kebele':
            print("❌ User not in kebele tenant")
            raise PermissionError("Only kebele leaders can create transfer requests")

        if user.role not in ['kebele_leader', 'kebele_admin']:
            print(f"❌ User role '{user.role}' not allowed")
            raise PermissionError("Only kebele leaders can create transfer requests")

        print("✅ perform_create validation passed")
        print(f"🔍 DEBUG: User: {user}, Tenant: {user.tenant}, Schema: {user.tenant.schema_name}")

        # Note: We don't validate citizen existence here during request creation
        # Validation will happen when the destination kebele accepts the request
        citizen_id = serializer.validated_data['citizen_id']
        print(f"🔍 DEBUG: Creating transfer request for citizen {citizen_id} from {user.tenant.name}")

        # Check for existing pending transfer in current schema
        existing_transfer = CitizenTransferRequest.objects.filter(
            source_kebele=user.tenant,
            citizen_id=citizen_id,
            status__in=['pending', 'accepted']
        ).first()

        if existing_transfer:
            raise ValueError("There is already a pending transfer request for this citizen")

        # Create transfer request in source schema
        source_transfer = serializer.save(
            source_kebele=user.tenant,
            requested_by=user
        )

        # Also create the same request in destination schema so destination kebele can see it
        destination_kebele = serializer.validated_data['destination_kebele']
        self._create_transfer_in_destination_schema(source_transfer, destination_kebele)

    @action(detail=True, methods=['post'])
    def force_sync_documents(self, request, pk=None):
        """Force sync documents to destination schema (debug endpoint)."""
        transfer_request = self.get_object()

        print(f"🔍 FORCE_SYNC_DOCUMENTS called for transfer {transfer_request.transfer_id}")

        # Force sync documents
        self._sync_documents_to_destination(transfer_request)

        # Check result
        destination_kebele = transfer_request.destination_kebele
        with schema_context(destination_kebele.schema_name):
            dest_transfer = CitizenTransferRequest.objects.filter(
                transfer_id=transfer_request.transfer_id
            ).first()

            if dest_transfer:
                return Response({
                    'message': 'Force sync completed',
                    'source_documents': {
                        'application_letter': bool(transfer_request.application_letter),
                        'current_kebele_id': bool(transfer_request.current_kebele_id),
                        'documents_uploaded_at': str(transfer_request.documents_uploaded_at)
                    },
                    'destination_documents': {
                        'application_letter': bool(dest_transfer.application_letter),
                        'current_kebele_id': bool(dest_transfer.current_kebele_id),
                        'documents_uploaded_at': str(dest_transfer.documents_uploaded_at)
                    }
                })
            else:
                return Response({
                    'error': 'Destination transfer not found',
                    'transfer_id': transfer_request.transfer_id
                }, status=404)

    @action(detail=False, methods=['get'])
    def debug_transfer_data(self, request):
        """Debug endpoint to check transfer request data in both schemas."""
        transfer_id = request.query_params.get('transfer_id')
        if not transfer_id:
            return Response({'error': 'transfer_id parameter required'}, status=400)

        result = {}

        # Get user's tenant to determine current schema
        user = request.user
        if not hasattr(user, 'tenant'):
            self._populate_user_tenant(request)

        if not hasattr(user, 'tenant'):
            return Response({'error': 'No tenant context available'}, status=400)

        current_schema = user.tenant.schema_name
        result['current_schema_name'] = current_schema

        print(f"🔍 DEBUG_TRANSFER_DATA: Current schema: {current_schema}")

        # Check current schema for transfer
        try:
            current_transfer = CitizenTransferRequest.objects.filter(transfer_id=transfer_id).first()
            if current_transfer:
                result['current_schema'] = {
                    'id': current_transfer.id,
                    'citizen_id': current_transfer.citizen_id,
                    'citizen_name': current_transfer.citizen_name,
                    'source_kebele': current_transfer.source_kebele.id if current_transfer.source_kebele else None,
                    'destination_kebele': current_transfer.destination_kebele.id if current_transfer.destination_kebele else None,
                    'status': current_transfer.status
                }

                # Now check the other schema (source or destination)
                other_kebele = None
                if current_transfer.source_kebele.id != user.tenant.id:
                    other_kebele = current_transfer.source_kebele
                    other_schema_type = 'source'
                else:
                    other_kebele = current_transfer.destination_kebele
                    other_schema_type = 'destination'

                # Check the other schema
                if other_kebele:
                    with schema_context(other_kebele.schema_name):
                        other_transfer = CitizenTransferRequest.objects.filter(transfer_id=transfer_id).first()
                        if other_transfer:
                            result[f'{other_schema_type}_schema'] = {
                                'id': other_transfer.id,
                                'citizen_id': other_transfer.citizen_id,
                                'citizen_name': other_transfer.citizen_name,
                                'source_kebele': other_transfer.source_kebele.id if other_transfer.source_kebele else None,
                                'destination_kebele': other_transfer.destination_kebele.id if other_transfer.destination_kebele else None,
                                'status': other_transfer.status
                            }

                            # Check if citizen exists in source schema (only if this is the source)
                            if other_schema_type == 'source' and other_transfer.citizen_id:
                                from citizens.models import Citizen  # Use citizens app model
                                try:
                                    citizen = Citizen.objects.get(id=other_transfer.citizen_id)
                                    result['source_citizen'] = {
                                        'id': str(citizen.id),  # Convert to string in case it's UUID
                                        'name': f"{citizen.first_name} {citizen.last_name}",
                                        'digital_id': citizen.digital_id,
                                        'is_active': citizen.is_active
                                    }
                                except Citizen.DoesNotExist:
                                    result['source_citizen'] = 'NOT_FOUND'
                            elif other_schema_type == 'source':
                                result['source_citizen'] = 'NO_CITIZEN_ID'
                        else:
                            result[f'{other_schema_type}_schema'] = 'NOT_FOUND'
            else:
                result['current_schema'] = 'NOT_FOUND'
        except Exception as e:
            result['error'] = f"Error accessing current schema: {str(e)}"
            print(f"❌ Error in debug_transfer_data: {e}")

        return Response(result)

    @action(detail=False, methods=['get'])
    def debug_by_transfer_id(self, request):
        """Debug endpoint to check dual-schema sync status by transfer_id."""
        transfer_id = request.query_params.get('transfer_id')
        if not transfer_id:
            return Response({'error': 'transfer_id parameter required'}, status=400)

        # Find transfer in current schema
        transfer_request = CitizenTransferRequest.objects.filter(transfer_id=transfer_id).first()
        if not transfer_request:
            return Response({'error': f'Transfer {transfer_id} not found in current schema'}, status=404)

        print(f"🔍 DEBUG_BY_TRANSFER_ID called for transfer {transfer_id}")
        print(f"  Found in current schema: ID {transfer_request.id}")
        print(f"  Source kebele: {transfer_request.source_kebele} (schema: {transfer_request.source_kebele.schema_name})")
        print(f"  Destination kebele: {transfer_request.destination_kebele} (schema: {transfer_request.destination_kebele.schema_name})")

        # Check destination schema
        destination_kebele = transfer_request.destination_kebele
        with schema_context(destination_kebele.schema_name):
            dest_transfers = CitizenTransferRequest.objects.filter(transfer_id=transfer_id)
            print(f"  Found {dest_transfers.count()} transfers in destination schema")

            dest_data = []
            for dt in dest_transfers:
                print(f"    - Transfer ID: {dt.id}, Status: {dt.status}")
                print(f"    - Application letter: {dt.application_letter}")
                print(f"    - Kebele ID: {dt.current_kebele_id}")
                print(f"    - Documents uploaded at: {dt.documents_uploaded_at}")

                dest_data.append({
                    'id': dt.id,
                    'application_letter': bool(dt.application_letter),
                    'current_kebele_id': bool(dt.current_kebele_id),
                    'documents_uploaded_at': dt.documents_uploaded_at
                })

        return Response({
            'transfer_id': transfer_id,
            'source_transfer': {
                'id': transfer_request.id,
                'application_letter': bool(transfer_request.application_letter),
                'current_kebele_id': bool(transfer_request.current_kebele_id),
                'documents_uploaded_at': transfer_request.documents_uploaded_at
            },
            'destination_transfers_count': dest_transfers.count(),
            'destination_transfers': dest_data
        })

    @action(detail=True, methods=['get'])
    def debug_sync(self, request, pk=None):
        """Debug endpoint to check dual-schema sync status."""
        transfer_request = self.get_object()

        print(f"🔍 DEBUG_SYNC called for transfer {transfer_request.transfer_id}")
        print(f"  Source kebele: {transfer_request.source_kebele} (schema: {transfer_request.source_kebele.schema_name})")
        print(f"  Destination kebele: {transfer_request.destination_kebele} (schema: {transfer_request.destination_kebele.schema_name})")

        # Check destination schema
        destination_kebele = transfer_request.destination_kebele
        with schema_context(destination_kebele.schema_name):
            dest_transfers = CitizenTransferRequest.objects.filter(
                transfer_id=transfer_request.transfer_id
            )
            print(f"  Found {dest_transfers.count()} transfers in destination schema")

            for dt in dest_transfers:
                print(f"    - Transfer ID: {dt.id}, Status: {dt.status}")
                print(f"    - Application letter: {dt.application_letter}")
                print(f"    - Kebele ID: {dt.current_kebele_id}")
                print(f"    - Documents uploaded at: {dt.documents_uploaded_at}")

        return Response({
            'source_transfer_id': transfer_request.id,
            'transfer_id': transfer_request.transfer_id,
            'source_documents': {
                'application_letter': bool(transfer_request.application_letter),
                'current_kebele_id': bool(transfer_request.current_kebele_id),
                'documents_uploaded_at': transfer_request.documents_uploaded_at
            },
            'destination_transfers_count': dest_transfers.count(),
            'destination_documents': [
                {
                    'id': dt.id,
                    'application_letter': bool(dt.application_letter),
                    'current_kebele_id': bool(dt.current_kebele_id),
                    'documents_uploaded_at': dt.documents_uploaded_at
                } for dt in dest_transfers
            ]
        })

    @action(detail=True, methods=['post'])
    def upload_documents(self, request, pk=None):
        """Upload application letter and kebele ID for transfer request."""
        print(f"🔍 UPLOAD_DOCUMENTS called:")
        print(f"  Transfer ID: {pk}")
        print(f"  User: {request.user}")
        print(f"  Files in request: {list(request.FILES.keys())}")
        print(f"  Request data: {request.data}")

        transfer_request = self.get_object()
        user = request.user

        print(f"  Transfer request: {transfer_request}")
        print(f"  Transfer status: {transfer_request.status}")
        print(f"  Source kebele: {transfer_request.source_kebele}")
        print(f"  User tenant: {getattr(user, 'tenant', 'NO_TENANT')}")

        # Validate permissions - only source kebele can upload documents
        if transfer_request.source_kebele != user.tenant:
            return Response(
                {'error': 'You can only upload documents for transfers from your kebele'},
                status=status.HTTP_403_FORBIDDEN
            )

        if user.role not in ['kebele_leader', 'kebele_admin', 'clerk']:
            return Response(
                {'error': 'Only kebele staff can upload documents'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if transfer is still pending
        if transfer_request.status != 'pending':
            return Response(
                {'error': 'Documents can only be uploaded for pending transfer requests'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Handle file uploads
        application_letter = request.FILES.get('application_letter')
        current_kebele_id = request.FILES.get('current_kebele_id')

        if not application_letter and not current_kebele_id:
            return Response(
                {'error': 'At least one document must be provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update transfer request with documents
        print(f"🔍 Updating transfer request with documents:")
        if application_letter:
            print(f"  Setting application_letter: {application_letter.name} ({application_letter.size} bytes)")
            transfer_request.application_letter = application_letter
        if current_kebele_id:
            print(f"  Setting current_kebele_id: {current_kebele_id.name} ({current_kebele_id.size} bytes)")
            transfer_request.current_kebele_id = current_kebele_id

        # Set upload timestamp
        transfer_request.documents_uploaded_at = timezone.now()
        print(f"🔍 Saving transfer request...")
        transfer_request.save()
        print(f"✅ Transfer request saved successfully")

        # Verify the save worked
        transfer_request.refresh_from_db()
        print(f"🔍 After save verification:")
        print(f"  application_letter: {transfer_request.application_letter}")
        print(f"  current_kebele_id: {transfer_request.current_kebele_id}")
        print(f"  documents_uploaded_at: {transfer_request.documents_uploaded_at}")

        # Also update the corresponding record in destination schema
        self._sync_documents_to_destination(transfer_request)

        return Response({
            'message': 'Documents uploaded successfully',
            'transfer': CitizenTransferRequestSerializer(transfer_request, context={'request': request}).data
        })

    @action(detail=True, methods=['post'])
    def review(self, request, pk=None):
        """Accept or reject a transfer request (Kebele B leader)."""
        transfer_request = self.get_object()
        user = request.user

        # Validate permissions
        if transfer_request.destination_kebele != user.tenant:
            return Response(
                {'error': 'You can only review transfers to your kebele'},
                status=status.HTTP_403_FORBIDDEN
            )

        if user.role not in ['kebele_leader', 'kebele_admin']:
            return Response(
                {'error': 'Only kebele leaders can review transfer requests'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = self.get_serializer(transfer_request, data=request.data)
        serializer.is_valid(raise_exception=True)

        action_type = serializer.validated_data['action']
        review_notes = serializer.validated_data.get('review_notes', '')

        with transaction.atomic():
            if action_type == 'accept':
                print(f"🔍 ACCEPTING transfer request {transfer_request.transfer_id}")

                # Perform the actual citizen transfer immediately upon acceptance
                try:
                    new_citizen_id = self._perform_citizen_transfer(transfer_request)
                    print(f"✅ Citizen transfer completed successfully. New citizen ID: {new_citizen_id}")

                    # Update transfer request status to completed
                    transfer_request.status = 'completed'
                    transfer_request.reviewed_by = user
                    transfer_request.reviewed_at = timezone.now()
                    transfer_request.review_notes = review_notes
                    transfer_request.completed_by = user
                    transfer_request.completed_at = timezone.now()
                    transfer_request.new_citizen_id = new_citizen_id
                    transfer_request.save()

                    # Sync status to source schema
                    self._sync_transfer_status_to_source(transfer_request)

                    return Response({
                        'message': 'Transfer request accepted and citizen transfer completed successfully',
                        'transfer': CitizenTransferRequestSerializer(transfer_request).data,
                        'new_citizen_id': new_citizen_id
                    })

                except Exception as e:
                    print(f"❌ Error during citizen transfer: {e}")
                    import traceback
                    print(f"❌ Traceback: {traceback.format_exc()}")

                    # If transfer fails, just accept the request without completing
                    transfer_request.status = 'accepted'
                    transfer_request.reviewed_by = user
                    transfer_request.reviewed_at = timezone.now()
                    transfer_request.review_notes = review_notes
                    transfer_request.save()

                    # Sync status to source schema
                    self._sync_transfer_status_to_source(transfer_request)

                    return Response({
                        'message': 'Transfer request accepted, but citizen transfer failed. Please try completing manually.',
                        'transfer': CitizenTransferRequestSerializer(transfer_request).data,
                        'error': str(e)
                    })

            elif action_type == 'reject':
                transfer_request.status = 'rejected'
                transfer_request.reviewed_by = user
                transfer_request.reviewed_at = timezone.now()
                transfer_request.review_notes = review_notes
                transfer_request.save()

                # Sync status to source schema
                self._sync_transfer_status_to_source(transfer_request)

                return Response({
                    'message': 'Transfer request rejected',
                    'transfer': CitizenTransferRequestSerializer(transfer_request).data
                })

    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Complete the transfer (Kebele A leader)."""
        transfer_request = self.get_object()
        user = request.user

        # Validate permissions
        if transfer_request.source_kebele != user.tenant:
            return Response(
                {'error': 'You can only complete transfers from your kebele'},
                status=status.HTTP_403_FORBIDDEN
            )

        if user.role not in ['kebele_leader', 'kebele_admin']:
            return Response(
                {'error': 'Only kebele leaders can complete transfers'},
                status=status.HTTP_403_FORBIDDEN
            )

        if not transfer_request.can_be_completed:
            return Response(
                {'error': 'This transfer request cannot be completed'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = self.get_serializer(transfer_request, data=request.data)
        serializer.is_valid(raise_exception=True)

        # Perform the actual transfer
        try:
            new_citizen_id = self._perform_citizen_transfer(transfer_request)

            with transaction.atomic():
                transfer_request.status = 'completed'
                transfer_request.completed_by = user
                transfer_request.completed_at = timezone.now()
                transfer_request.new_citizen_id = new_citizen_id
                transfer_request.save()

                return Response({
                    'message': 'Citizen transfer completed successfully',
                    'transfer': CitizenTransferRequestSerializer(transfer_request).data,
                    'new_citizen_id': new_citizen_id
                })

        except Exception as e:
            return Response(
                {'error': f'Transfer failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _perform_citizen_transfer(self, transfer_request):
        """
        Perform the actual citizen transfer:
        1. Get citizen data from source kebele
        2. Create citizen in destination kebele with all related data
        3. Mark original citizen as inactive (for history/reporting)
        4. Create transfer history record
        """
        print(f"🔍 Starting citizen transfer for {transfer_request.transfer_id}")

        source_kebele = transfer_request.source_kebele
        destination_kebele = transfer_request.destination_kebele

        # Get citizen_id from source schema if not available in current transfer request
        citizen_id = transfer_request.citizen_id
        if not citizen_id:
            print(f"🔍 citizen_id is None, getting from source schema")
            with schema_context(source_kebele.schema_name):
                source_transfer = CitizenTransferRequest.objects.filter(
                    transfer_id=transfer_request.transfer_id
                ).first()
                if source_transfer and source_transfer.citizen_id:
                    citizen_id = source_transfer.citizen_id
                    print(f"✅ Found citizen_id in source schema: {citizen_id}")
                else:
                    raise ValueError("Could not find citizen_id in source schema")

        # Step 1: Get citizen data from source kebele
        print(f"🔍 Step 1: Getting citizen data from source kebele {source_kebele.schema_name}")
        with schema_context(source_kebele.schema_name):
            try:
                from citizens.models import Citizen  # Use same model as citizen registration
                citizen = Citizen.objects.prefetch_related(
                    'documents', 'emergency_contacts', 'parents', 'children', 'spouse_records'
                ).get(id=citizen_id, is_active=True)

                print(f"✅ Found citizen: {citizen.first_name} {citizen.last_name}")

                # Serialize citizen data for transfer
                citizen_data = self._serialize_citizen_for_transfer(citizen)
                print(f"✅ Serialized citizen data with {len(citizen_data.get('emergency_contacts', []))} emergency contacts")

            except Citizen.DoesNotExist:
                raise ValueError("Citizen not found or already inactive in source kebele")

        # Step 2: Create citizen in destination kebele
        print(f"🔍 Step 2: Creating citizen in destination kebele {destination_kebele.schema_name}")
        with schema_context(destination_kebele.schema_name):
            new_citizen = self._create_citizen_in_destination(citizen_data, destination_kebele)
            new_citizen_id = new_citizen.id
            print(f"✅ Created new citizen with ID: {new_citizen_id}, Digital ID: {new_citizen.digital_id}")

        # Step 3: Mark original citizen as transferred (with full tracking info)
        print(f"🔍 Step 3: Marking original citizen as transferred in source kebele")
        with schema_context(source_kebele.schema_name):
            # Store original digital ID before marking as transferred
            original_digital_id = citizen.digital_id

            # Update transfer tracking fields
            citizen.is_active = False
            citizen.transfer_status = 'transferred'
            citizen.transfer_date = timezone.now()
            citizen.transfer_destination_kebele_id = destination_kebele.id
            citizen.transfer_destination_kebele_name = destination_kebele.name
            citizen.transfer_reason = transfer_request.transfer_reason
            citizen.transfer_request_id = transfer_request.transfer_id
            citizen.original_digital_id = original_digital_id
            # Add new deactivation fields
            citizen.deactivation_reason = 'transfer_completed'
            citizen.deactivated_at = timezone.now()
            citizen.save()
            print(f"✅ Original citizen marked as transferred to {destination_kebele.name}")

        # Step 4: Update transfer request with new citizen ID in destination schema
        print(f"🔍 Step 4: Updating transfer request in destination schema")
        with schema_context(destination_kebele.schema_name):
            dest_transfer = CitizenTransferRequest.objects.filter(
                transfer_id=transfer_request.transfer_id
            ).first()
            if dest_transfer:
                dest_transfer.citizen_id = new_citizen_id
                dest_transfer.save()
                print(f"✅ Updated destination transfer request with new citizen ID")

        print(f"✅ Citizen transfer completed successfully")
        return new_citizen_id

    def _serialize_citizen_for_transfer(self, citizen):
        """Serialize citizen data for transfer to another kebele."""
        print(f"🔍 Serializing citizen data for {citizen.first_name} {citizen.last_name}")

        # Get basic citizen data (same model used in both source and destination)
        citizen_data = {
            'first_name': citizen.first_name,
            'last_name': citizen.last_name,
            'middle_name': citizen.middle_name,
            'first_name_am': citizen.first_name_am,
            'last_name_am': citizen.last_name_am,
            'middle_name_am': citizen.middle_name_am,
            'gender': citizen.gender,
            'date_of_birth': citizen.date_of_birth.isoformat() if citizen.date_of_birth else None,
            'nationality': citizen.nationality,
            'marital_status': citizen.marital_status,
            'blood_type': citizen.blood_type,
            'disability': citizen.disability,
            'phone': citizen.phone,
            'email': citizen.email,
            'house_number': citizen.house_number,
            'photo': citizen.photo,  # Base64 encoded photo
            'is_active': True,  # New citizen should be active
            'religion': citizen.religion,
            'subcity': citizen.subcity,
            'ketena': citizen.ketena,
            'status': citizen.status,
            'region': citizen.region,
            'employment': citizen.employment,
            'employee_type': citizen.employee_type,
            'organization_name': citizen.organization_name,
            'is_resident': citizen.is_resident,
            # Note: Using same citizens.models.Citizen for both source and destination
        }

        # Add related data
        citizen_data['documents'] = []
        citizen_data['emergency_contacts'] = []
        citizen_data['parents'] = []
        citizen_data['children'] = []
        citizen_data['spouse'] = None

        # Serialize documents
        print(f"🔍 Serializing {citizen.documents.count()} documents")
        for doc in citizen.documents.all():
            citizen_data['documents'].append({
                'document_type': doc.document_type.id,
                'document_file': doc.document_file.url if doc.document_file else None,
                'issue_date': doc.issue_date.isoformat() if doc.issue_date else None,
                'expiry_date': doc.expiry_date.isoformat() if doc.expiry_date else None,
                'is_active': doc.is_active
            })

        # Serialize emergency contacts
        print(f"🔍 Serializing {citizen.emergency_contacts.count()} emergency contacts")
        for contact in citizen.emergency_contacts.all():
            citizen_data['emergency_contacts'].append({
                'first_name': contact.first_name,
                'last_name': contact.last_name,
                'middle_name': getattr(contact, 'middle_name', None),
                'first_name_am': getattr(contact, 'first_name_am', None),
                'last_name_am': getattr(contact, 'last_name_am', None),
                'middle_name_am': getattr(contact, 'middle_name_am', None),
                'relationship': contact.relationship,  # This is a CharField, not ForeignKey
                'phone': getattr(contact, 'phone', None),
                'email': contact.email,
                'nationality': contact.nationality,
                'primary_contact': getattr(contact, 'primary_contact', False),
                'is_active': contact.is_active,
            })

        # Serialize spouse (if exists)
        spouse_records = citizen.spouse_records.all()
        if spouse_records.exists():
            spouse = spouse_records.first()
            print(f"🔍 Serializing spouse: {spouse.first_name} {spouse.last_name}")
            citizen_data['spouse'] = {
                'first_name': spouse.first_name,
                'last_name': spouse.last_name,
                'middle_name': spouse.middle_name,
                'first_name_am': getattr(spouse, 'first_name_am', None),
                'last_name_am': getattr(spouse, 'last_name_am', None),
                'middle_name_am': getattr(spouse, 'middle_name_am', None),
                'phone': spouse.phone,
                'email': spouse.email,
                'nationality': spouse.nationality,
                'is_resident': spouse.is_resident,
                'is_active': spouse.is_active,
            }

        # Serialize parents
        print(f"🔍 Serializing {citizen.parents.count()} parents")
        for parent in citizen.parents.all():
            citizen_data['parents'].append({
                'first_name': parent.first_name,
                'last_name': parent.last_name,
                'middle_name': parent.middle_name,
                'first_name_am': getattr(parent, 'first_name_am', None),
                'last_name_am': getattr(parent, 'last_name_am', None),
                'middle_name_am': getattr(parent, 'middle_name_am', None),
                'gender': parent.gender,
                'date_of_birth': getattr(parent, 'date_of_birth', None),
                'phone': getattr(parent, 'phone', None),
                'email': parent.email,
                'nationality': parent.nationality,
                'is_resident': parent.is_resident,
                'is_active': parent.is_active,
            })

        # Serialize children
        print(f"🔍 Serializing {citizen.children.count()} children")
        for child in citizen.children.all():
            citizen_data['children'].append({
                'first_name': child.first_name,
                'last_name': child.last_name,
                'middle_name': child.middle_name,
                'first_name_am': getattr(child, 'first_name_am', None),
                'last_name_am': getattr(child, 'last_name_am', None),
                'middle_name_am': getattr(child, 'middle_name_am', None),
                'gender': getattr(child, 'gender', None),
                'date_of_birth': child.date_of_birth.isoformat() if child.date_of_birth else None,
                'nationality': child.nationality,
                'is_resident': child.is_resident,
                'is_active': child.is_active,
            })

        print(f"✅ Citizen data serialization completed")
        return citizen_data

    def _create_citizen_in_destination(self, citizen_data, destination_kebele):
        """Create citizen in destination kebele with transferred data and all related records."""
        from citizens.models import Citizen, EmergencyContact, Parent, Child, Spouse, Document
        from datetime import datetime

        print(f"🔍 Creating citizen in destination kebele: {destination_kebele.name}")

        # Extract related data before creating main citizen
        documents_data = citizen_data.pop('documents', [])
        emergency_contacts_data = citizen_data.pop('emergency_contacts', [])
        parents_data = citizen_data.pop('parents', [])
        children_data = citizen_data.pop('children', [])
        spouse_data = citizen_data.pop('spouse', None)

        # Convert date strings back to date objects
        if citizen_data.get('date_of_birth'):
            citizen_data['date_of_birth'] = datetime.fromisoformat(citizen_data['date_of_birth']).date()

        # Set kebele reference (use ID, not object)
        citizen_data['kebele'] = destination_kebele.id

        # Create new citizen
        print(f"🔍 Creating main citizen record")
        citizen = Citizen.objects.create(**citizen_data)

        # Generate new digital ID based on destination kebele format
        citizen.generate_digital_id()
        citizen.save()
        print(f"✅ Created citizen with new digital ID: {citizen.digital_id}")

        # Create spouse if exists
        if spouse_data:
            print(f"🔍 Creating spouse record")
            # Remove fields that don't exist in the Spouse model
            spouse_data.pop('date_of_birth', None)  # Spouse model doesn't have date_of_birth

            spouse = Spouse.objects.create(citizen=citizen, **spouse_data)
            print(f"✅ Created spouse: {spouse.first_name} {spouse.last_name}")

        # Create emergency contacts
        print(f"🔍 Creating {len(emergency_contacts_data)} emergency contacts")
        for contact_data in emergency_contacts_data:
            try:
                # relationship is a CharField, not a ForeignKey, so keep it as is
                EmergencyContact.objects.create(citizen=citizen, **contact_data)
            except Exception as e:
                print(f"⚠️ Failed to create emergency contact: {e}")
                print(f"⚠️ Contact data: {contact_data}")

        # Create parents
        print(f"🔍 Creating {len(parents_data)} parents")
        for parent_data in parents_data:
            try:
                # Convert date string if exists
                if parent_data.get('date_of_birth'):
                    parent_data['date_of_birth'] = datetime.fromisoformat(parent_data['date_of_birth']).date()

                Parent.objects.create(citizen=citizen, **parent_data)
            except Exception as e:
                print(f"⚠️ Failed to create parent: {e}")
                print(f"⚠️ Parent data: {parent_data}")

        # Create children
        print(f"🔍 Creating {len(children_data)} children")
        for child_data in children_data:
            try:
                # Convert date string if exists
                if child_data.get('date_of_birth'):
                    child_data['date_of_birth'] = datetime.fromisoformat(child_data['date_of_birth']).date()

                Child.objects.create(citizen=citizen, **child_data)
            except Exception as e:
                print(f"⚠️ Failed to create child: {e}")
                print(f"⚠️ Child data: {child_data}")

        # Create documents
        print(f"🔍 Creating {len(documents_data)} documents")
        for doc_data in documents_data:
            try:
                # Convert date strings
                if doc_data.get('issue_date'):
                    doc_data['issue_date'] = datetime.fromisoformat(doc_data['issue_date']).date()
                if doc_data.get('expiry_date'):
                    doc_data['expiry_date'] = datetime.fromisoformat(doc_data['expiry_date']).date()

                # document_type is stored as integer ID, not ForeignKey
                Document.objects.create(citizen=citizen, **doc_data)
            except Exception as e:
                print(f"⚠️ Failed to create document: {e}")
                print(f"⚠️ Document data: {doc_data}")

        print(f"✅ Citizen creation completed with all related records")
        return citizen

    def _create_transfer_in_destination_schema(self, source_transfer, destination_kebele):
        """Create a copy of the transfer request in the destination kebele schema."""
        print(f"🔍 DEBUG: Creating transfer copy in destination schema {destination_kebele.schema_name}")

        with schema_context(destination_kebele.schema_name):
            # Create a copy of the transfer request in destination schema
            destination_transfer = CitizenTransferRequest.objects.create(
                transfer_id=source_transfer.transfer_id,  # Same transfer ID to link them
                citizen_id=source_transfer.citizen_id,
                citizen_name=source_transfer.citizen_name,
                citizen_digital_id=source_transfer.citizen_digital_id,
                source_kebele=source_transfer.source_kebele,
                destination_kebele=source_transfer.destination_kebele,
                transfer_reason=source_transfer.transfer_reason,
                reason_description=source_transfer.reason_description,
                status=source_transfer.status,
                requested_by=source_transfer.requested_by,
                created_at=source_transfer.created_at
            )
            print(f"✅ Created transfer copy in destination: {destination_transfer.transfer_id}")
            return destination_transfer

    def _sync_transfer_status_to_source(self, destination_transfer):
        """Sync transfer status from destination schema back to source schema."""
        print(f"🔍 DEBUG: Syncing status to source schema for transfer {destination_transfer.transfer_id}")

        with schema_context(destination_transfer.source_kebele.schema_name):
            try:
                source_transfer = CitizenTransferRequest.objects.get(
                    transfer_id=destination_transfer.transfer_id
                )
                source_transfer.status = destination_transfer.status
                source_transfer.reviewed_by = destination_transfer.reviewed_by
                source_transfer.reviewed_at = destination_transfer.reviewed_at
                source_transfer.review_notes = destination_transfer.review_notes
                source_transfer.save()
                print(f"✅ Synced status to source schema: {source_transfer.status}")
            except CitizenTransferRequest.DoesNotExist:
                print(f"❌ Source transfer not found for ID: {destination_transfer.transfer_id}")

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a transfer request."""
        transfer_request = self.get_object()
        user = request.user

        # Only source kebele leader can cancel
        if transfer_request.source_kebele != user.tenant:
            return Response(
                {'error': 'You can only cancel transfers from your kebele'},
                status=status.HTTP_403_FORBIDDEN
            )

        if not transfer_request.can_be_cancelled:
            return Response(
                {'error': 'This transfer request cannot be cancelled'},
                status=status.HTTP_400_BAD_REQUEST
            )

        transfer_request.status = 'cancelled'
        transfer_request.save()

        return Response({
            'message': 'Transfer request cancelled successfully',
            'transfer': CitizenTransferRequestSerializer(transfer_request).data
        })

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get transfer statistics for the user's kebele."""
        user = request.user

        if not hasattr(user, 'tenant') or user.tenant.type != 'kebele':
            return Response({'error': 'Only available for kebele users'}, status=400)

        kebele = user.tenant

        # Get transfer counts
        outgoing = CitizenTransferRequest.objects.filter(source_kebele=kebele)
        incoming = CitizenTransferRequest.objects.filter(destination_kebele=kebele)

        stats = {
            'total_requests': outgoing.count() + incoming.count(),
            'pending_requests': outgoing.filter(status='pending').count() + incoming.filter(status='pending').count(),
            'accepted_requests': outgoing.filter(status='accepted').count() + incoming.filter(status='accepted').count(),
            'rejected_requests': outgoing.filter(status='rejected').count() + incoming.filter(status='rejected').count(),
            'completed_transfers': outgoing.filter(status='completed').count() + incoming.filter(status='completed').count(),
            'outgoing_total': outgoing.count(),
            'outgoing_pending': outgoing.filter(status='pending').count(),
            'outgoing_completed': outgoing.filter(status='completed').count(),
            'incoming_total': incoming.count(),
            'incoming_pending': incoming.filter(status='pending').count(),
            'incoming_completed': incoming.filter(status='completed').count(),
            'net_transfer': incoming.filter(status='completed').count() - outgoing.filter(status='completed').count()
        }

        return Response(stats)

    @action(detail=False, methods=['get'])
    def available_kebeles(self, request):
        """Get kebeles available for transfer (same city, different kebele)."""
        user = request.user

        if not hasattr(user, 'tenant') or user.tenant.type != 'kebele':
            return Response({'error': 'Only available for kebele users'}, status=400)

        try:
            # Get the current user's kebele
            current_kebele = user.tenant

            # Get the subcity (parent of kebele)
            if not current_kebele.parent:
                return Response({'error': 'Kebele has no parent subcity'}, status=400)

            subcity = current_kebele.parent

            # Get the city (parent of subcity)
            if not subcity.parent:
                return Response({'error': 'Subcity has no parent city'}, status=400)

            city = subcity.parent

            # Get all kebeles in the same city
            available_kebeles = []

            # Get all subcities in the city
            subcities_in_city = Tenant.objects.filter(
                parent=city,
                type='subcity'
            )

            # Get all kebeles in those subcities (excluding current kebele)
            all_kebeles = Tenant.objects.filter(
                parent__in=subcities_in_city,
                type='kebele'
            ).exclude(id=current_kebele.id).select_related('parent')

            # Get all domains for these kebeles in one query
            from tenants.models.tenant import Domain
            kebele_ids = [kebele.id for kebele in all_kebeles]
            domains_dict = {}
            if kebele_ids:
                domains = Domain.objects.filter(tenant_id__in=kebele_ids, is_primary=True)
                domains_dict = {domain.tenant_id: domain.domain for domain in domains}

            # Build the response
            for kebele in all_kebeles:
                available_kebeles.append({
                    'id': kebele.id,
                    'name': kebele.name,
                    'domain': domains_dict.get(kebele.id),
                    'subcity_id': kebele.parent.id,
                    'subcity_name': kebele.parent.name,
                    'city_id': city.id,
                    'city_name': city.name
                })

            return Response({
                'current_kebele': {
                    'id': current_kebele.id,
                    'name': current_kebele.name,
                    'subcity_name': subcity.name,
                    'city_name': city.name
                },
                'available_kebeles': available_kebeles,
                'total_count': len(available_kebeles)
            })

        except Exception as e:
            return Response({
                'error': f'Failed to fetch available kebeles: {str(e)}'
            }, status=500)

    def _sync_documents_to_destination(self, transfer_request):
        """Sync uploaded documents to the corresponding record in destination schema."""
        print(f"🔍 SYNC_DOCUMENTS_TO_DESTINATION called:")
        print(f"  Transfer ID: {transfer_request.transfer_id}")
        print(f"  Source kebele: {transfer_request.source_kebele}")
        print(f"  Destination kebele: {transfer_request.destination_kebele}")
        print(f"  Destination schema: {transfer_request.destination_kebele.schema_name}")

        try:
            destination_kebele = transfer_request.destination_kebele

            with schema_context(destination_kebele.schema_name):
                print(f"🔍 Searching for transfer in destination schema...")

                # Debug: Show all transfers in destination schema
                all_transfers = CitizenTransferRequest.objects.all()
                print(f"🔍 All transfers in destination schema ({len(all_transfers)}):")
                for t in all_transfers:
                    print(f"  - ID: {t.id}, Transfer ID: {t.transfer_id}, Source: {t.source_kebele}, Dest: {t.destination_kebele}")

                # Find the corresponding transfer request in destination schema
                dest_transfer = CitizenTransferRequest.objects.filter(
                    transfer_id=transfer_request.transfer_id,
                    source_kebele=transfer_request.source_kebele,
                    destination_kebele=transfer_request.destination_kebele
                ).first()

                if dest_transfer:
                    print(f"✅ Found destination transfer: {dest_transfer.id}")
                    print(f"🔍 Before sync - dest_transfer documents:")
                    print(f"  application_letter: {dest_transfer.application_letter}")
                    print(f"  current_kebele_id: {dest_transfer.current_kebele_id}")

                    # Copy document references (files are stored in shared location)
                    dest_transfer.application_letter = transfer_request.application_letter
                    dest_transfer.current_kebele_id = transfer_request.current_kebele_id
                    dest_transfer.documents_uploaded_at = transfer_request.documents_uploaded_at
                    dest_transfer.save()

                    print(f"🔍 After sync - dest_transfer documents:")
                    print(f"  application_letter: {dest_transfer.application_letter}")
                    print(f"  current_kebele_id: {dest_transfer.current_kebele_id}")
                    print(f"  documents_uploaded_at: {dest_transfer.documents_uploaded_at}")
                    print(f"✅ Documents synced to destination schema: {destination_kebele.schema_name}")
                else:
                    print(f"❌ Could not find corresponding transfer in destination schema")
                    print(f"❌ Looking for: transfer_id={transfer_request.transfer_id}, source={transfer_request.source_kebele.id}, dest={transfer_request.destination_kebele.id}")

        except Exception as e:
            print(f"❌ Error syncing documents to destination: {e}")
            import traceback
            print(f"❌ Traceback: {traceback.format_exc()}")
            # Don't raise exception - document sync failure shouldn't break the upload


