import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
} from '@mui/material';
import { Info as InfoIcon } from '@mui/icons-material';
import EthiopianCalendarWidget from '../../../components/common/EthiopianCalendarWidget';

const AdditionalInfoStep = ({ formik, loading }) => {
  return (
      <Card>
        {/* Header with blue background */}
        <Box sx={{
          bgcolor: '#2196f3',
          color: 'white',
          p: 2,
          display: 'flex',
          alignItems: 'center'
        }}>
          <InfoIcon sx={{ mr: 1 }} />
          <Typography variant="h6" component="h2">
            Additional Information
          </Typography>
        </Box>

        <CardContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Provide additional information about the citizen including employment and other relevant data.
          </Typography>

          <Grid container spacing={3}>
            {/* Employment Information */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={formik.touched.employment && Boolean(formik.errors.employment)}>
                <InputLabel id="employment-label">Employment Status</InputLabel>
                <Select
                  labelId="employment-label"
                  id="employment"
                  name="employment"
                  value={formik.values.employment}
                  label="Employment Status"
                  onChange={formik.handleChange}
                >
                  <MenuItem value="employed">Employed</MenuItem>
                  <MenuItem value="unemployed">Unemployed</MenuItem>
                  <MenuItem value="self_employed">Self Employed</MenuItem>
                  <MenuItem value="student">Student</MenuItem>
                  <MenuItem value="retired">Retired</MenuItem>
                </Select>
                {formik.touched.employment && formik.errors.employment && (
                  <FormHelperText>{formik.errors.employment}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="organization_name"
                name="organization_name"
                label="Organization Name"
                value={formik.values.organization_name}
                onChange={formik.handleChange}
                error={formik.touched.organization_name && Boolean(formik.errors.organization_name)}
                helperText={formik.touched.organization_name && formik.errors.organization_name}
              />
            </Grid>

            {/* Additional Fields */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={formik.touched.employee_type && Boolean(formik.errors.employee_type)}>
                <InputLabel id="employee-type-label">Employee Type</InputLabel>
                <Select
                  labelId="employee-type-label"
                  id="employee_type"
                  name="employee_type"
                  value={formik.values.employee_type}
                  label="Employee Type"
                  onChange={formik.handleChange}
                >
                  <MenuItem value="permanent">Permanent</MenuItem>
                  <MenuItem value="contract">Contract</MenuItem>
                  <MenuItem value="temporary">Temporary</MenuItem>
                  <MenuItem value="part_time">Part Time</MenuItem>
                  <MenuItem value="freelance">Freelance</MenuItem>
                </Select>
                {formik.touched.employee_type && formik.errors.employee_type && (
                  <FormHelperText>{formik.errors.employee_type}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="region"
                name="region"
                label="Region"
                value={formik.values.region}
                onChange={formik.handleChange}
                error={formik.touched.region && Boolean(formik.errors.region)}
                helperText={formik.touched.region && formik.errors.region}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
  );
};

export default AdditionalInfoStep;
