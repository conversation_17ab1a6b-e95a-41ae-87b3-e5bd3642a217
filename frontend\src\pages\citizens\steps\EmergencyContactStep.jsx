import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  Switch,
  FormControlLabel,
  IconButton,
  Paper,
  InputAdornment,
  CircularProgress,
  FormHelperText,
} from '@mui/material';
import {
  Contacts as ContactsIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import SharedDataDropdown from '../../../components/common/SharedDataDropdown';
import ResidentAutocomplete from '../../../components/common/ResidentAutocomplete';

const EmergencyContactStep = ({
  formik,
  loading,
  searchResults,
  searchLoading,
  searchResident,
  selectResident
}) => {
  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <ContactsIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" component="h2">
            Emergency Contact
          </Typography>
        </Box>

        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formik.values.is_emergency_contact_resident}
                  onChange={(e) => {
                    formik.setFieldValue('is_emergency_contact_resident', e.target.checked);
                    if (!e.target.checked) {
                      formik.setFieldValue('emergency_contact_id', '');
                    }
                  }}
                  color="primary"
                />
              }
              label="Emergency contact is a resident of this kebele"
            />
          </Grid>
        </Grid>

        {formik.values.is_emergency_contact_resident ? (
          // Search for existing resident
          <Box>
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} sm={8}>
                <ResidentAutocomplete
                  label="Search for Emergency Contact"
                  placeholder="Type to search for emergency contact..."
                  value={formik.values.emergency_contact_id ? {
                    id: formik.values.emergency_contact_id, // This is the database ID
                    first_name: formik.values.emergency_contact_first_name,
                    middle_name: formik.values.emergency_contact_middle_name,
                    last_name: formik.values.emergency_contact_last_name,
                    full_name: `${formik.values.emergency_contact_first_name} ${formik.values.emergency_contact_middle_name} ${formik.values.emergency_contact_last_name}`.trim(),
                    digital_id: formik.values.emergency_contact_digital_id || `EMERGENCY_${formik.values.emergency_contact_id}` // Use actual digital_id if available
                  } : null}
                  onChange={(event, newValue) => {
                    if (newValue) {
                      formik.setValues({
                        ...formik.values,
                        emergency_contact_id: newValue.id, // Use database id for linked_citizen
                        emergency_contact_digital_id: newValue.digital_id, // Store digital_id for display
                        emergency_contact_first_name: newValue.first_name,
                        emergency_contact_middle_name: newValue.middle_name,
                        emergency_contact_last_name: newValue.last_name,
                        // Copy contact info from selected resident
                        emergency_contact_phone: newValue.phone || '',
                        emergency_contact_email: newValue.email || '',
                      });
                    } else {
                      formik.setValues({
                        ...formik.values,
                        emergency_contact_id: '',
                        emergency_contact_digital_id: '',
                        emergency_contact_first_name: '',
                        emergency_contact_middle_name: '',
                        emergency_contact_last_name: '',
                        emergency_contact_phone: '',
                        emergency_contact_email: '',
                      });
                    }
                  }}
                  required={formik.values.is_emergency_contact_resident}
                  error={formik.touched.emergency_contact_id && Boolean(formik.errors.emergency_contact_id)}
                  helperText={formik.touched.emergency_contact_id && formik.errors.emergency_contact_id}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <SharedDataDropdown
                  dataType="relationships"
                  label="Relationship"
                  name="emergency_contact_relation"
                  id="emergency_contact_relation"
                  value={formik.values.emergency_contact_relation}
                  onChange={formik.handleChange}
                  error={formik.touched.emergency_contact_relation && formik.errors.emergency_contact_relation}
                  disabled={loading}
                  required
                />
              </Grid>
            </Grid>
          </Box>
        ) : (
          // Manual entry form
          <Grid container spacing={2}>
            {/* Name Fields */}
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                id="emergency_contact_first_name"
                name="emergency_contact_first_name"
                label="First Name"
                value={formik.values.emergency_contact_first_name}
                onChange={formik.handleChange}
                error={formik.touched.emergency_contact_first_name && Boolean(formik.errors.emergency_contact_first_name)}
                helperText={formik.touched.emergency_contact_first_name && formik.errors.emergency_contact_first_name}
                required={!formik.values.is_emergency_contact_resident}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                id="emergency_contact_middle_name"
                name="emergency_contact_middle_name"
                label="Middle Name"
                value={formik.values.emergency_contact_middle_name}
                onChange={formik.handleChange}
                error={formik.touched.emergency_contact_middle_name && Boolean(formik.errors.emergency_contact_middle_name)}
                helperText={formik.touched.emergency_contact_middle_name && formik.errors.emergency_contact_middle_name}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                id="emergency_contact_last_name"
                name="emergency_contact_last_name"
                label="Last Name"
                value={formik.values.emergency_contact_last_name}
                onChange={formik.handleChange}
                error={formik.touched.emergency_contact_last_name && Boolean(formik.errors.emergency_contact_last_name)}
                helperText={formik.touched.emergency_contact_last_name && formik.errors.emergency_contact_last_name}
                required={!formik.values.is_emergency_contact_resident}
              />
            </Grid>

            {/* Contact Information */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="emergency_contact_phone"
                name="emergency_contact_phone"
                label="Phone Number"
                value={formik.values.emergency_contact_phone}
                onChange={formik.handleChange}
                error={formik.touched.emergency_contact_phone && Boolean(formik.errors.emergency_contact_phone)}
                helperText={formik.touched.emergency_contact_phone && formik.errors.emergency_contact_phone}
                required={!formik.values.is_emergency_contact_resident}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="emergency_contact_email"
                name="emergency_contact_email"
                label="Email"
                type="email"
                value={formik.values.emergency_contact_email}
                onChange={formik.handleChange}
                error={formik.touched.emergency_contact_email && Boolean(formik.errors.emergency_contact_email)}
                helperText={formik.touched.emergency_contact_email && formik.errors.emergency_contact_email}
              />
            </Grid>

            {/* Relationship */}
            <Grid item xs={12}>
              <SharedDataDropdown
                dataType="relationships"
                label="Relationship"
                name="emergency_contact_relation"
                id="emergency_contact_relation"
                value={formik.values.emergency_contact_relation}
                onChange={formik.handleChange}
                error={formik.touched.emergency_contact_relation && formik.errors.emergency_contact_relation}
                disabled={loading}
                required
              />
            </Grid>
          </Grid>
        )}
      </CardContent>
    </Card>
  );
};

export default EmergencyContactStep;
