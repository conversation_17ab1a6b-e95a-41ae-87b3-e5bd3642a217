import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Alert,
  CircularProgress,
  Divider,
  Switch,
  FormControlLabel,
  IconButton,
  Collapse,
  Paper,
  InputAdornment,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Person as PersonIcon,
  PersonAdd as PersonAddIcon,
  PersonSearch as PersonSearchIcon,
  Contacts as ContactsIcon,
  People as PeopleIcon,
  FamilyRestroom as FamilyIcon,
  ChildCare as ChildIcon,
} from '@mui/icons-material';
import { EthiopianCalendarWidget } from '../../components/common/EthiopianCalendarWidget';
import { useFormik } from 'formik';
import * as yup from 'yup';
import axios from '../../utils/axios';
import { useSharedData } from '../../contexts/SharedDataContext';
import SharedDataDropdown from '../../components/common/SharedDataDropdown';
import { getStandardMenuProps, createSelectChangeHandler } from '../../utils/dropdownUtils';

const validationSchema = yup.object({
  // Personal information
  first_name: yup.string().required('First name is required'),
  middle_name: yup.string().required('Middle name is required'),
  last_name: yup.string().required('Last name is required'),
  date_of_birth: yup.date().required('Date of birth is required'),
  gender: yup.string().required('Gender is required'),
  phone_number: yup.string().matches(/^\+?[0-9]{10,15}$/, 'Invalid phone number'),
  email: yup.string().email('Invalid email address'),

  // Additional information
  nationality: yup.string(),
  marital_status: yup.string(),
  occupation: yup.string(),
  blood_type: yup.string(),
  house_number: yup.string(),
  street: yup.string(),
  ketena: yup.string(),
  religion: yup.string(),
  citizen_status: yup.string().required('Citizen status is required'),

  // Emergency contact
  is_emergency_contact_resident: yup.boolean(),
  emergency_contact_id: yup.string().when('is_emergency_contact_resident', {
    is: true,
    then: yup.string().required('Emergency contact ID is required when resident'),
    otherwise: yup.string()
  }),
  emergency_contact_name: yup.string().when('is_emergency_contact_resident', {
    is: false,
    then: yup.string().required('Emergency contact name is required'),
    otherwise: yup.string()
  }),
  emergency_contact_phone: yup.string().when('is_emergency_contact_resident', {
    is: false,
    then: yup.string().matches(/^\+?[0-9]{10,15}$/, 'Invalid phone number').required('Emergency contact phone is required'),
    otherwise: yup.string()
  }),
  emergency_contact_relation: yup.string().required('Relationship is required'),

  // Spouse information
  has_spouse: yup.boolean(),
  is_spouse_resident: yup.boolean(),
  spouse_id: yup.string().when(['has_spouse', 'is_spouse_resident'], {
    is: (has_spouse, is_spouse_resident) => has_spouse && is_spouse_resident,
    then: yup.string().required('Spouse ID is required when resident'),
    otherwise: yup.string()
  }),
  spouse_first_name: yup.string().when(['has_spouse', 'is_spouse_resident'], {
    is: (has_spouse, is_spouse_resident) => has_spouse && !is_spouse_resident,
    then: yup.string().required('Spouse first name is required'),
    otherwise: yup.string()
  }),
  spouse_middle_name: yup.string().when(['has_spouse', 'is_spouse_resident'], {
    is: (has_spouse, is_spouse_resident) => has_spouse && !is_spouse_resident,
    then: yup.string().required('Spouse middle name is required'),
    otherwise: yup.string()
  }),
  spouse_last_name: yup.string().when(['has_spouse', 'is_spouse_resident'], {
    is: (has_spouse, is_spouse_resident) => has_spouse && !is_spouse_resident,
    then: yup.string().required('Spouse last name is required'),
    otherwise: yup.string()
  }),

  // Parent information
  has_parents: yup.boolean(),
  is_father_resident: yup.boolean(),
  father_id: yup.string().when(['has_parents', 'is_father_resident'], {
    is: (has_parents, is_father_resident) => has_parents && is_father_resident,
    then: yup.string().required('Father ID is required when resident'),
    otherwise: yup.string()
  }),
  father_first_name: yup.string().when(['has_parents', 'is_father_resident'], {
    is: (has_parents, is_father_resident) => has_parents && !is_father_resident,
    then: yup.string().required('Father first name is required'),
    otherwise: yup.string()
  }),
  father_middle_name: yup.string().when(['has_parents', 'is_father_resident'], {
    is: (has_parents, is_father_resident) => has_parents && !is_father_resident,
    then: yup.string().required('Father middle name is required'),
    otherwise: yup.string()
  }),
  father_last_name: yup.string().when(['has_parents', 'is_father_resident'], {
    is: (has_parents, is_father_resident) => has_parents && !is_father_resident,
    then: yup.string().required('Father last name is required'),
    otherwise: yup.string()
  }),

  is_mother_resident: yup.boolean(),
  mother_id: yup.string().when(['has_parents', 'is_mother_resident'], {
    is: (has_parents, is_mother_resident) => has_parents && is_mother_resident,
    then: yup.string().required('Mother ID is required when resident'),
    otherwise: yup.string()
  }),
  mother_first_name: yup.string().when(['has_parents', 'is_mother_resident'], {
    is: (has_parents, is_mother_resident) => has_parents && !is_mother_resident,
    then: yup.string().required('Mother first name is required'),
    otherwise: yup.string()
  }),
  mother_middle_name: yup.string().when(['has_parents', 'is_mother_resident'], {
    is: (has_parents, is_mother_resident) => has_parents && !is_mother_resident,
    then: yup.string().required('Mother middle name is required'),
    otherwise: yup.string()
  }),
  mother_last_name: yup.string().when(['has_parents', 'is_mother_resident'], {
    is: (has_parents, is_mother_resident) => has_parents && !is_mother_resident,
    then: yup.string().required('Mother last name is required'),
    otherwise: yup.string()
  }),

  // Children information
  has_children: yup.boolean(),
  children: yup.array().of(
    yup.object().shape({
      is_resident: yup.boolean(),
      child_id: yup.string().when('is_resident', {
        is: true,
        then: yup.string().required('Child ID is required when resident'),
        otherwise: yup.string()
      }),
      first_name: yup.string().when('is_resident', {
        is: false,
        then: yup.string().required('Child first name is required'),
        otherwise: yup.string()
      }),
      middle_name: yup.string().when('is_resident', {
        is: false,
        then: yup.string().required('Child middle name is required'),
        otherwise: yup.string()
      }),
      last_name: yup.string().when('is_resident', {
        is: false,
        then: yup.string().required('Child last name is required'),
        otherwise: yup.string()
      }),
      date_of_birth: yup.date().when('is_resident', {
        is: false,
        then: yup.date().required('Child date of birth is required'),
        otherwise: yup.date()
      }),
      gender: yup.string().when('is_resident', {
        is: false,
        then: yup.string().required('Child gender is required'),
        otherwise: yup.string()
      })
    })
  )
});

const CitizenCreate = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const { sharedData, loading: sharedDataLoading } = useSharedData();

  // Helper function to check if marital status is single
  const isMaritalStatusSingle = (maritalStatusId) => {
    if (!maritalStatusId || !sharedData.maritalStatuses) return false;
    const maritalStatus = sharedData.maritalStatuses.find(status =>
      String(status.id) === String(maritalStatusId)
    );
    return maritalStatus?.name?.toLowerCase().includes('single') ||
           maritalStatus?.name?.toLowerCase().includes('unmarried');
  };

  // State for expandable sections
  const [expandedSections, setExpandedSections] = useState({
    spouse: false,
    parents: false,
    children: false
  });

  // State for resident search
  const [searchResults, setSearchResults] = useState({
    emergencyContact: [],
    spouse: [],
    father: [],
    mother: [],
    children: []
  });

  // State for search loading
  const [searchLoading, setSearchLoading] = useState({
    emergencyContact: false,
    spouse: false,
    father: false,
    mother: false,
    children: false
  });

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Function to search for residents
  const searchResident = async (type, searchTerm) => {
    if (!searchTerm || searchTerm.length < 3) {
      return;
    }

    setSearchLoading(prev => ({ ...prev, [type]: true }));

    try {
      // In a real implementation, this would call an API endpoint
      // For now, we'll just simulate a search with a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock search results
      const mockResults = [
        { id: '1', first_name: 'John', middle_name: 'David', last_name: 'Smith', id_number: 'ETH-12345' },
        { id: '2', first_name: 'Sarah', middle_name: 'Jane', last_name: 'Johnson', id_number: 'ETH-67890' },
        { id: '3', first_name: 'Michael', middle_name: 'Robert', last_name: 'Williams', id_number: 'ETH-24680' }
      ];

      setSearchResults(prev => ({ ...prev, [type]: mockResults }));
    } catch (err) {
      console.error(`Error searching for ${type}:`, err);
    } finally {
      setSearchLoading(prev => ({ ...prev, [type]: false }));
    }
  };

  // Function to select a resident from search results
  const selectResident = (type, resident) => {
    if (type === 'emergencyContact') {
      formik.setValues({
        ...formik.values,
        emergency_contact_id: resident.id,
        emergency_contact_name: `${resident.first_name} ${resident.middle_name} ${resident.last_name}`,
      });
    } else if (type === 'spouse') {
      formik.setValues({
        ...formik.values,
        spouse_id: resident.id,
        spouse_first_name: resident.first_name,
        spouse_middle_name: resident.middle_name,
        spouse_last_name: resident.last_name,
      });
    } else if (type === 'father') {
      formik.setValues({
        ...formik.values,
        father_id: resident.id,
        father_first_name: resident.first_name,
        father_middle_name: resident.middle_name,
        father_last_name: resident.last_name,
      });
    } else if (type === 'mother') {
      formik.setValues({
        ...formik.values,
        mother_id: resident.id,
        mother_first_name: resident.first_name,
        mother_middle_name: resident.middle_name,
        mother_last_name: resident.last_name,
      });
    }

    // Clear search results after selection
    setSearchResults(prev => ({ ...prev, [type]: [] }));
  };

  // Function to add a child
  const addChild = () => {
    formik.setValues({
      ...formik.values,
      children: [
        ...formik.values.children,
        {
          is_resident: false,
          child_id: '',
          first_name: '',
          middle_name: '',
          last_name: '',
          date_of_birth: null,
          gender: ''
        }
      ]
    });
  };

  // Function to remove a child
  const removeChild = (index) => {
    const updatedChildren = [...formik.values.children];
    updatedChildren.splice(index, 1);
    formik.setValues({
      ...formik.values,
      children: updatedChildren
    });
  };

  const formik = useFormik({
    initialValues: {
      // Personal information (English)
      first_name: '',
      middle_name: '',
      last_name: '',

      // Personal information (Amharic)
      first_name_am: '',
      middle_name_am: '',
      last_name_am: '',

      // Basic info
      digital_id: '',
      date_of_birth: null,
      gender: '',
      phone: '',
      email: '',

      // ID Information
      id_issue_date: null,
      id_expiry_date: null,

      // Address information
      house_number: '',
      street: '',
      ketena: '',
      subcity: '',
      kebele: '',
      region: '',

      // Additional information
      nationality: '',
      religion: '',
      marital_status: '',
      citizen_status: '',
      employment: '',
      employee_type: '',
      organization_name: '',
      blood_type: '',

      // Status
      current_status: '',
      is_resident: true,
      is_active: true,

      // Emergency contact
      is_emergency_contact_resident: false,
      emergency_contact_id: '',
      emergency_contact_name: '',
      emergency_contact_phone: '',
      emergency_contact_relation: '',

      // Spouse information
      has_spouse: false,
      is_spouse_resident: false,
      spouse_id: '',
      spouse_first_name: '',
      spouse_middle_name: '',
      spouse_last_name: '',

      // Parent information
      has_parents: false,
      is_father_resident: false,
      father_id: '',
      father_first_name: '',
      father_middle_name: '',
      father_last_name: '',

      is_mother_resident: false,
      mother_id: '',
      mother_first_name: '',
      mother_middle_name: '',
      mother_last_name: '',

      // Children information
      has_children: false,
      children: [],
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        setError('');

        // In a real implementation, this would call an API endpoint
        // For now, we'll just simulate a successful request
        await new Promise(resolve => setTimeout(resolve, 1000));

        setSuccess(true);
        setTimeout(() => {
          navigate('/citizens');
        }, 1500);
      } catch (err) {
        setError(err.response?.data?.detail || 'Failed to create citizen. Please try again.');
      } finally {
        setLoading(false);
      }
    },
  });

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Register New Citizen
        </Typography>
        <Button
          variant="outlined"
          onClick={() => navigate('/citizens')}
        >
          Cancel
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Citizen registered successfully!
        </Alert>
      )}

      <form onSubmit={formik.handleSubmit}>
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Personal Information
            </Typography>

            {/* English Names */}
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 2, mb: 1, color: 'primary.main' }}>
              Name (English)
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  required
                  id="first_name"
                  name="first_name"
                  label="First Name"
                  value={formik.values.first_name}
                  onChange={formik.handleChange}
                  error={formik.touched.first_name && Boolean(formik.errors.first_name)}
                  helperText={formik.touched.first_name && formik.errors.first_name}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  required
                  id="middle_name"
                  name="middle_name"
                  label="Middle Name"
                  value={formik.values.middle_name}
                  onChange={formik.handleChange}
                  error={formik.touched.middle_name && Boolean(formik.errors.middle_name)}
                  helperText={formik.touched.middle_name && formik.errors.middle_name}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  required
                  id="last_name"
                  name="last_name"
                  label="Last Name"
                  value={formik.values.last_name}
                  onChange={formik.handleChange}
                  error={formik.touched.last_name && Boolean(formik.errors.last_name)}
                  helperText={formik.touched.last_name && formik.errors.last_name}
                />
              </Grid>
            </Grid>

            {/* Amharic Names */}
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3, mb: 1, color: 'primary.main' }}>
              Name (Amharic) - Optional
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  id="first_name_am"
                  name="first_name_am"
                  label="First Name (Amharic)"
                  value={formik.values.first_name_am}
                  onChange={formik.handleChange}
                  error={formik.touched.first_name_am && Boolean(formik.errors.first_name_am)}
                  helperText={formik.touched.first_name_am && formik.errors.first_name_am}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  id="middle_name_am"
                  name="middle_name_am"
                  label="Middle Name (Amharic)"
                  value={formik.values.middle_name_am}
                  onChange={formik.handleChange}
                  error={formik.touched.middle_name_am && Boolean(formik.errors.middle_name_am)}
                  helperText={formik.touched.middle_name_am && formik.errors.middle_name_am}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  id="last_name_am"
                  name="last_name_am"
                  label="Last Name (Amharic)"
                  value={formik.values.last_name_am}
                  onChange={formik.handleChange}
                  error={formik.touched.last_name_am && Boolean(formik.errors.last_name_am)}
                  helperText={formik.touched.last_name_am && formik.errors.last_name_am}
                />
              </Grid>
            </Grid>

            {/* Basic Details */}
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3, mb: 1, color: 'primary.main' }}>
              Basic Details
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  required
                  id="digital_id"
                  name="digital_id"
                  label="Digital ID"
                  value={formik.values.digital_id}
                  onChange={formik.handleChange}
                  error={formik.touched.digital_id && Boolean(formik.errors.digital_id)}
                  helperText={formik.touched.digital_id && formik.errors.digital_id}
                  placeholder="e.g., DID-2024-001"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <EthiopianCalendarWidget
                  key="citizen-birth-date-v2"
                  label="Date of Birth"
                  value={formik.values.date_of_birth}
                  onChange={(value) => formik.setFieldValue('date_of_birth', value)}
                  error={formik.touched.date_of_birth && Boolean(formik.errors.date_of_birth)}
                  helperText={formik.touched.date_of_birth && formik.errors.date_of_birth}
                  language="en"
                  placeholder="Select birth date"
                  disableFuture={true}
                  minYear={1950}
                  maxYear={2020}
                  required
                  showToggle={true}
                  showConversion={true}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={formik.touched.gender && Boolean(formik.errors.gender)}>
                  <InputLabel id="gender-label">Gender</InputLabel>
                  <Select
                    labelId="gender-label"
                    id="gender"
                    name="gender"
                    value={formik.values.gender}
                    label="Gender"
                    onChange={createSelectChangeHandler('gender', formik.handleChange)}
                    MenuProps={getStandardMenuProps()}
                  >
                    <MenuItem value="male">Male</MenuItem>
                    <MenuItem value="female">Female</MenuItem>
                  </Select>
                  {formik.touched.gender && formik.errors.gender && (
                    <FormHelperText>{formik.errors.gender}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>

            {/* ID Information */}
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3, mb: 1, color: 'primary.main' }}>
              ID Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <EthiopianCalendarWidget
                  key="citizen-id-issue-date-v2"
                  label="ID Issue Date"
                  value={formik.values.id_issue_date}
                  onChange={(value) => formik.setFieldValue('id_issue_date', value)}
                  error={formik.touched.id_issue_date && Boolean(formik.errors.id_issue_date)}
                  helperText={formik.touched.id_issue_date && formik.errors.id_issue_date}
                  language="en"
                  placeholder="Select ID issue date"
                  minYear={2000}
                  maxYear={2030}
                  showToggle={true}
                  showConversion={true}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <EthiopianCalendarWidget
                  key="citizen-id-expiry-date-v2"
                  label="ID Expiry Date"
                  value={formik.values.id_expiry_date}
                  onChange={(value) => formik.setFieldValue('id_expiry_date', value)}
                  error={formik.touched.id_expiry_date && Boolean(formik.errors.id_expiry_date)}
                  helperText={formik.touched.id_expiry_date && formik.errors.id_expiry_date}
                  language="en"
                  placeholder="Select ID expiry date"
                  minYear={2000}
                  maxYear={2040}
                  showToggle={true}
                  showConversion={true}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Contact Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="phone"
                  name="phone"
                  label="Phone Number"
                  value={formik.values.phone}
                  onChange={formik.handleChange}
                  error={formik.touched.phone && Boolean(formik.errors.phone)}
                  helperText={formik.touched.phone && formik.errors.phone}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="email"
                  name="email"
                  label="Email Address"
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  error={formik.touched.email && Boolean(formik.errors.email)}
                  helperText={formik.touched.email && formik.errors.email}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  id="house_number"
                  name="house_number"
                  label="House Number"
                  value={formik.values.house_number}
                  onChange={formik.handleChange}
                  error={formik.touched.house_number && Boolean(formik.errors.house_number)}
                  helperText={formik.touched.house_number && formik.errors.house_number}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  id="street"
                  name="street"
                  label="Street"
                  value={formik.values.street}
                  onChange={formik.handleChange}
                  error={formik.touched.street && Boolean(formik.errors.street)}
                  helperText={formik.touched.street && formik.errors.street}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <SharedDataDropdown
                  dataType="ketenas"
                  label="Ketena"
                  name="ketena"
                  id="ketena"
                  value={formik.values.ketena}
                  onChange={formik.handleChange}
                  error={formik.touched.ketena && formik.errors.ketena}
                  disabled={loading || sharedDataLoading}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Additional Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <SharedDataDropdown
                  dataType="countries"
                  label="Nationality"
                  name="nationality"
                  id="nationality"
                  value={formik.values.nationality}
                  onChange={formik.handleChange}
                  error={formik.touched.nationality && formik.errors.nationality}
                  disabled={loading || sharedDataLoading}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <SharedDataDropdown
                  dataType="citizenStatuses"
                  label="Citizen Status"
                  name="citizen_status"
                  id="citizen_status"
                  value={formik.values.citizen_status}
                  onChange={formik.handleChange}
                  error={formik.touched.citizen_status && formik.errors.citizen_status}
                  disabled={loading || sharedDataLoading}
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <SharedDataDropdown
                  dataType="religions"
                  label="Religion"
                  name="religion"
                  id="religion"
                  value={formik.values.religion}
                  onChange={formik.handleChange}
                  error={formik.touched.religion && formik.errors.religion}
                  disabled={loading || sharedDataLoading}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <SharedDataDropdown
                  dataType="maritalStatuses"
                  label="Marital Status"
                  name="marital_status"
                  id="marital_status"
                  value={formik.values.marital_status}
                  onChange={formik.handleChange}
                  error={formik.touched.marital_status && formik.errors.marital_status}
                  disabled={loading || sharedDataLoading}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <SharedDataDropdown
                  dataType="employmentTypes"
                  label="Employment Type"
                  name="employment"
                  id="employment"
                  value={formik.values.employment}
                  onChange={formik.handleChange}
                  error={formik.touched.employment && formik.errors.employment}
                  disabled={loading || sharedDataLoading}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="organization_name"
                  name="organization_name"
                  label="Organization Name"
                  value={formik.values.organization_name}
                  onChange={formik.handleChange}
                  error={formik.touched.organization_name && Boolean(formik.errors.organization_name)}
                  helperText={formik.touched.organization_name && formik.errors.organization_name}
                  placeholder="e.g., Ministry of Health"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={formik.touched.blood_type && Boolean(formik.errors.blood_type)}>
                  <InputLabel id="blood-type-label">Blood Type</InputLabel>
                  <Select
                    labelId="blood-type-label"
                    id="blood_type"
                    name="blood_type"
                    value={formik.values.blood_type}
                    label="Blood Type"
                    onChange={createSelectChangeHandler('blood_type', formik.handleChange)}
                    MenuProps={getStandardMenuProps()}
                  >
                    <MenuItem value="A+">A+</MenuItem>
                    <MenuItem value="A-">A-</MenuItem>
                    <MenuItem value="B+">B+</MenuItem>
                    <MenuItem value="B-">B-</MenuItem>
                    <MenuItem value="AB+">AB+</MenuItem>
                    <MenuItem value="AB-">AB-</MenuItem>
                    <MenuItem value="O+">O+</MenuItem>
                    <MenuItem value="O-">O-</MenuItem>
                    <MenuItem value="unknown">Unknown</MenuItem>
                  </Select>
                  {formik.touched.blood_type && formik.errors.blood_type && (
                    <FormHelperText>{formik.errors.blood_type}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <ContactsIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6" component="h2">
                Emergency Contact
              </Typography>
            </Box>

            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formik.values.is_emergency_contact_resident}
                      onChange={(e) => {
                        formik.setFieldValue('is_emergency_contact_resident', e.target.checked);
                        if (!e.target.checked) {
                          formik.setFieldValue('emergency_contact_id', '');
                        }
                      }}
                      color="primary"
                    />
                  }
                  label="Emergency contact is a resident of this kebele"
                />
              </Grid>
            </Grid>

            {formik.values.is_emergency_contact_resident ? (
              // Search for existing resident
              <Box>
                <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Search for resident
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={8}>
                      <TextField
                        fullWidth
                        label="Search by name or ID"
                        placeholder="Enter at least 3 characters"
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => searchResident('emergencyContact', document.getElementById('emergency_contact_search').value)}
                                disabled={searchLoading.emergencyContact}
                              >
                                {searchLoading.emergencyContact ? <CircularProgress size={24} /> : <SearchIcon />}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                        id="emergency_contact_search"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        id="emergency_contact_relation"
                        name="emergency_contact_relation"
                        label="Relationship"
                        select
                        value={formik.values.emergency_contact_relation}
                        onChange={formik.handleChange}
                        error={formik.touched.emergency_contact_relation && Boolean(formik.errors.emergency_contact_relation)}
                        helperText={formik.touched.emergency_contact_relation && formik.errors.emergency_contact_relation}
                        required
                      >
                        <MenuItem value="spouse">Spouse</MenuItem>
                        <MenuItem value="parent">Parent</MenuItem>
                        <MenuItem value="child">Child</MenuItem>
                        <MenuItem value="sibling">Sibling</MenuItem>
                        <MenuItem value="friend">Friend</MenuItem>
                        <MenuItem value="other">Other</MenuItem>
                      </TextField>
                    </Grid>
                  </Grid>

                  {/* Search results */}
                  {searchResults.emergencyContact.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Search Results
                      </Typography>
                      <Paper variant="outlined" sx={{ maxHeight: 200, overflow: 'auto' }}>
                        {searchResults.emergencyContact.map((resident) => (
                          <Box
                            key={resident.id}
                            sx={{
                              p: 1,
                              borderBottom: '1px solid',
                              borderColor: 'divider',
                              '&:hover': { bgcolor: 'action.hover' },
                              cursor: 'pointer',
                            }}
                            onClick={() => selectResident('emergencyContact', resident)}
                          >
                            <Typography variant="body2">
                              <strong>{resident.first_name} {resident.middle_name} {resident.last_name}</strong> ({resident.id_number})
                            </Typography>
                          </Box>
                        ))}
                      </Paper>
                    </Box>
                  )}

                  {/* Selected resident */}
                  {formik.values.emergency_contact_id && (
                    <Box sx={{ mt: 2, p: 2, bgcolor: 'action.hover', borderRadius: 1 }}>
                      <Typography variant="subtitle2">
                        Selected Contact: {formik.values.emergency_contact_name}
                      </Typography>
                      <Typography variant="caption">
                        ID: {formik.values.emergency_contact_id}
                      </Typography>
                    </Box>
                  )}
                </Paper>

                {formik.touched.emergency_contact_id && formik.errors.emergency_contact_id && (
                  <FormHelperText error>{formik.errors.emergency_contact_id}</FormHelperText>
                )}
              </Box>
            ) : (
              // Manual entry form
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    id="emergency_contact_name"
                    name="emergency_contact_name"
                    label="Contact Name"
                    value={formik.values.emergency_contact_name}
                    onChange={formik.handleChange}
                    error={formik.touched.emergency_contact_name && Boolean(formik.errors.emergency_contact_name)}
                    helperText={formik.touched.emergency_contact_name && formik.errors.emergency_contact_name}
                    required={!formik.values.is_emergency_contact_resident}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    id="emergency_contact_phone"
                    name="emergency_contact_phone"
                    label="Contact Phone"
                    value={formik.values.emergency_contact_phone}
                    onChange={formik.handleChange}
                    error={formik.touched.emergency_contact_phone && Boolean(formik.errors.emergency_contact_phone)}
                    helperText={formik.touched.emergency_contact_phone && formik.errors.emergency_contact_phone}
                    required={!formik.values.is_emergency_contact_resident}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <SharedDataDropdown
                    dataType="relationships"
                    label="Relationship"
                    name="emergency_contact_relation"
                    id="emergency_contact_relation"
                    value={formik.values.emergency_contact_relation}
                    onChange={formik.handleChange}
                    error={formik.touched.emergency_contact_relation && formik.errors.emergency_contact_relation}
                    disabled={loading || sharedDataLoading}
                    required
                  />
                </Grid>
              </Grid>
            )}
          </CardContent>
        </Card>

        {/* Spouse Information - Only show if not single */}
        {formik.values.marital_status && !isMaritalStatusSingle(formik.values.marital_status) && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  mb: 2,
                  cursor: 'pointer'
                }}
                onClick={() => toggleSection('spouse')}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6" component="h2">
                    Spouse Information
                  </Typography>
                </Box>
                <IconButton>
                  {expandedSections.spouse ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Box>

            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formik.values.has_spouse}
                      onChange={(e) => {
                        formik.setFieldValue('has_spouse', e.target.checked);
                        if (!e.target.checked) {
                          formik.setFieldValue('is_spouse_resident', false);
                          formik.setFieldValue('spouse_id', '');
                          formik.setFieldValue('spouse_first_name', '');
                          formik.setFieldValue('spouse_middle_name', '');
                          formik.setFieldValue('spouse_last_name', '');
                        }
                      }}
                      color="primary"
                    />
                  }
                  label="Citizen has a spouse"
                />
              </Grid>
            </Grid>

            <Collapse in={formik.values.has_spouse && expandedSections.spouse}>
              {formik.values.has_spouse && (
                <>
                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formik.values.is_spouse_resident}
                            onChange={(e) => {
                              formik.setFieldValue('is_spouse_resident', e.target.checked);
                              if (!e.target.checked) {
                                formik.setFieldValue('spouse_id', '');
                              }
                            }}
                            color="primary"
                            disabled={!formik.values.has_spouse}
                          />
                        }
                        label="Spouse is a resident of this kebele"
                      />
                    </Grid>
                  </Grid>

                  {formik.values.is_spouse_resident ? (
                    // Search for existing resident
                    <Box>
                      <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Search for spouse
                        </Typography>
                        <Grid container spacing={2}>
                          <Grid item xs={12}>
                            <TextField
                              fullWidth
                              label="Search by name or ID"
                              placeholder="Enter at least 3 characters"
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    <IconButton
                                      onClick={() => searchResident('spouse', document.getElementById('spouse_search').value)}
                                      disabled={searchLoading.spouse}
                                    >
                                      {searchLoading.spouse ? <CircularProgress size={24} /> : <SearchIcon />}
                                    </IconButton>
                                  </InputAdornment>
                                ),
                              }}
                              id="spouse_search"
                            />
                          </Grid>
                        </Grid>

                        {/* Search results */}
                        {searchResults.spouse.length > 0 && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="subtitle2" gutterBottom>
                              Search Results
                            </Typography>
                            <Paper variant="outlined" sx={{ maxHeight: 200, overflow: 'auto' }}>
                              {searchResults.spouse.map((resident) => (
                                <Box
                                  key={resident.id}
                                  sx={{
                                    p: 1,
                                    borderBottom: '1px solid',
                                    borderColor: 'divider',
                                    '&:hover': { bgcolor: 'action.hover' },
                                    cursor: 'pointer',
                                  }}
                                  onClick={() => selectResident('spouse', resident)}
                                >
                                  <Typography variant="body2">
                                    <strong>{resident.first_name} {resident.middle_name} {resident.last_name}</strong> ({resident.id_number})
                                  </Typography>
                                </Box>
                              ))}
                            </Paper>
                          </Box>
                        )}

                        {/* Selected resident */}
                        {formik.values.spouse_id && (
                          <Box sx={{ mt: 2, p: 2, bgcolor: 'action.hover', borderRadius: 1 }}>
                            <Typography variant="subtitle2">
                              Selected Spouse: {formik.values.spouse_first_name} {formik.values.spouse_middle_name} {formik.values.spouse_last_name}
                            </Typography>
                            <Typography variant="caption">
                              ID: {formik.values.spouse_id}
                            </Typography>
                          </Box>
                        )}
                      </Paper>

                      {formik.touched.spouse_id && formik.errors.spouse_id && (
                        <FormHelperText error>{formik.errors.spouse_id}</FormHelperText>
                      )}
                    </Box>
                  ) : (
                    // Manual entry form
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          id="spouse_first_name"
                          name="spouse_first_name"
                          label="First Name"
                          value={formik.values.spouse_first_name}
                          onChange={formik.handleChange}
                          error={formik.touched.spouse_first_name && Boolean(formik.errors.spouse_first_name)}
                          helperText={formik.touched.spouse_first_name && formik.errors.spouse_first_name}
                          required={formik.values.has_spouse && !formik.values.is_spouse_resident}
                        />
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          id="spouse_middle_name"
                          name="spouse_middle_name"
                          label="Middle Name"
                          value={formik.values.spouse_middle_name}
                          onChange={formik.handleChange}
                          error={formik.touched.spouse_middle_name && Boolean(formik.errors.spouse_middle_name)}
                          helperText={formik.touched.spouse_middle_name && formik.errors.spouse_middle_name}
                          required={formik.values.has_spouse && !formik.values.is_spouse_resident}
                        />
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          id="spouse_last_name"
                          name="spouse_last_name"
                          label="Last Name"
                          value={formik.values.spouse_last_name}
                          onChange={formik.handleChange}
                          error={formik.touched.spouse_last_name && Boolean(formik.errors.spouse_last_name)}
                          helperText={formik.touched.spouse_last_name && formik.errors.spouse_last_name}
                          required={formik.values.has_spouse && !formik.values.is_spouse_resident}
                        />
                      </Grid>
                    </Grid>
                  )}
                </>
              )}
            </Collapse>
          </CardContent>
        </Card>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
          <Button
            type="submit"
            variant="contained"
            size="large"
            disabled={loading}
            sx={{ minWidth: 150 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Register Citizen'}
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default CitizenCreate;
