#!/bin/bash

# Backend startup script with robust migration handling
set -e

echo "🚀 Starting GoID Backend..."

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
python manage.py shell -c "
import time
from django.db import connection, OperationalError

max_retries = 30
retry_count = 0

while retry_count < max_retries:
    try:
        connection.ensure_connection()
        print('✅ Database connection successful')
        break
    except OperationalError as e:
        retry_count += 1
        print(f'❌ Database not ready (attempt {retry_count}/{max_retries}): {e}')
        time.sleep(2)

if retry_count >= max_retries:
    print('❌ Failed to connect to database after maximum retries')
    exit(1)
"

# Clean up migration cache
echo "🧹 Cleaning migration cache..."
find . -path "*/migrations/__pycache__" -exec rm -rf {} + 2>/dev/null || true

# Generate fresh migrations
echo "📝 Generating fresh migrations..."
python manage.py makemigrations users tenants shared citizens idcards workflows

# Run shared schema migrations first
echo "🔧 Running shared schema migrations..."
python manage.py migrate_schemas --shared

# Populate shared data for APIs
echo "📊 Populating shared data for APIs..."
python manage.py populate_shared_data

# Create public tenant if it doesn't exist
echo "🏢 Ensuring public tenant exists..."
python manage.py shell -c "
from tenants.models import Tenant, Domain, TenantType
try:
    if not Tenant.objects.filter(schema_name='public').exists():
        tenant = Tenant(schema_name='public', name='Public', type=TenantType.CITY)
        tenant.save()
        domain = Domain(domain='localhost', tenant=tenant, is_primary=True)
        domain.save()
        print('✅ Public tenant created')
    else:
        print('✅ Public tenant already exists')
except Exception as e:
    print(f'⚠️  Public tenant creation issue (may be normal): {e}')
"

# Run tenant schema migrations
echo "🔧 Running tenant schema migrations..."
python manage.py migrate_schemas --tenant

# Initialize sample data
echo "📊 Initializing sample data..."
python manage.py init_data

# Collect static files
echo "📁 Collecting static files..."
python manage.py collectstatic --noinput

# Start the Django development server
echo "🌐 Starting Django development server..."
python manage.py runserver 0.0.0.0:8000
