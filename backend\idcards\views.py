from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.http import HttpResponse
from django.utils import timezone
import qrcode
from io import BytesIO
import base64
import json
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A8
from reportlab.lib.units import mm
from reportlab.lib import colors
from django.core.files.base import ContentFile
from PIL import Image
import uuid
import datetime
from .models import IDCard, IDCardTemplate, IDCardStatus
from .serializers import IDCardSerializer, IDCardTemplateSerializer, IDCardListSerializer, IDCardStatusUpdateSerializer, IDCardApprovalSerializer
from .filters import IDCardFilter
from .photo_processing import photo_processor
from common.permissions import CanManageIDCards, IsKebeleAdminOrHigher, IsSubcityAdminOrHigher, CanApproveIDCards
from workflows.models import WorkflowLog


class IDCardTemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing ID Card Templates.

    Permissions:
    - All authenticated users can view templates (needed for ID card creation)
    - Only kebele leaders and higher can create/modify templates
    - Templates are tenant-specific
    """
    queryset = IDCardTemplate.objects.all()
    serializer_class = IDCardTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]  # Allow all authenticated users to view
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        """Filter templates by tenant (handled by schema-based multi-tenancy)"""
        return IDCardTemplate.objects.filter(is_active=True)

    def get_permissions(self):
        """
        Instantiate and return the list of permissions that this view requires.
        """
        if self.action in ['create', 'update', 'partial_update', 'destroy', 'set_default']:
            # Only kebele admins and higher can modify templates
            permission_classes = [permissions.IsAuthenticated, IsKebeleAdminOrHigher]
        else:
            # All authenticated users can view templates and seed defaults
            permission_classes = [permissions.IsAuthenticated]

        return [permission() for permission in permission_classes]

    @action(detail=True, methods=['post'], permission_classes=[IsKebeleAdminOrHigher])
    def set_default(self, request, pk=None):
        """Set this template as the default template for the tenant"""
        template = self.get_object()

        # Remove default from other templates
        IDCardTemplate.objects.filter(is_default=True).update(is_default=False)

        # Set this template as default
        template.is_default = True
        template.save()

        return Response({'detail': 'Template set as default successfully'})

    @action(detail=False, methods=['get'])
    def default(self, request):
        """Get the default template for the current tenant"""
        try:
            default_template = IDCardTemplate.objects.get(is_default=True, is_active=True)
            serializer = self.get_serializer(default_template)
            return Response(serializer.data)
        except IDCardTemplate.DoesNotExist:
            # Return the first active template if no default is set
            try:
                first_template = IDCardTemplate.objects.filter(is_active=True).first()
                if first_template:
                    serializer = self.get_serializer(first_template)
                    return Response(serializer.data)
                else:
                    return Response({'detail': 'No templates available'}, status=status.HTTP_404_NOT_FOUND)
            except:
                return Response({'detail': 'No templates available'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def seed_defaults(self, request):
        """Create default templates for the current tenant if they don't exist"""
        try:
            # Check if templates already exist
            existing_count = IDCardTemplate.objects.count()
            if existing_count > 0:
                # Return existing templates
                existing_templates = IDCardTemplate.objects.filter(is_active=True)
                serializer = self.get_serializer(existing_templates, many=True)
                return Response({
                    'detail': f'Templates already exist ({existing_count} templates)',
                    'templates_count': existing_count,
                    'templates': serializer.data
                })

            # Create default templates
            templates_data = [
                {
                    'name': 'Default Template',
                    'description': 'Standard ID card template with official government styling',
                    'background_color': '#FFFFFF',
                    'text_color': '#000000',
                    'accent_color': '#1976D2',
                    'logo_position': 'top_left',
                    'photo_position': 'left',
                    'header_text': 'Federal Democratic Republic of Ethiopia',
                    'subtitle_text': 'National ID Card',
                    'footer_text': '',
                    'is_active': True,
                    'is_default': True
                },
                {
                    'name': 'Modern Template',
                    'description': 'Modern ID card template with contemporary design',
                    'background_color': '#F5F5F5',
                    'text_color': '#212121',
                    'accent_color': '#2196F3',
                    'logo_position': 'top_center',
                    'photo_position': 'right',
                    'header_text': 'Federal Democratic Republic of Ethiopia',
                    'subtitle_text': 'Digital ID Card',
                    'footer_text': 'Valid for official identification',
                    'is_active': True,
                    'is_default': False
                },
                {
                    'name': 'Classic Template',
                    'description': 'Traditional ID card template with classic styling',
                    'background_color': '#FAFAFA',
                    'text_color': '#333333',
                    'accent_color': '#4CAF50',
                    'logo_position': 'top_left',
                    'photo_position': 'left',
                    'header_text': 'Federal Democratic Republic of Ethiopia',
                    'subtitle_text': 'Official ID Card',
                    'footer_text': 'Government of Ethiopia',
                    'is_active': True,
                    'is_default': False
                }
            ]

            created_templates = []
            for template_data in templates_data:
                template = IDCardTemplate.objects.create(**template_data)
                created_templates.append(template)

            # Serialize the created templates
            serializer = self.get_serializer(created_templates, many=True)

            return Response({
                'detail': f'Successfully created {len(created_templates)} default templates',
                'templates_count': len(created_templates),
                'templates': serializer.data
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'detail': f'Error creating default templates: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class IDCardViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing ID cards.

    Permissions:
    - Clerks can generate ID cards, view ID cards list, and view ID card details
    - Kebele leaders can approve/reject ID cards, view ID cards list, and view ID card details
    - Subcity admins can view ID cards list and details from their child kebeles
    - City admins can view ID cards list and details from their child subcities
    - Super admins have full access to all ID cards
    """
    queryset = IDCard.objects.all()
    serializer_class = IDCardSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageIDCards]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = IDCardFilter
    search_fields = ['card_number', 'citizen__first_name', 'citizen__middle_name', 'citizen__last_name']
    ordering_fields = ['created_at', 'issue_date', 'expiry_date', 'status']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return different serializers for different actions."""
        if self.action == 'list':
            return IDCardListSerializer
        elif self.action == 'update_status':
            return IDCardStatusUpdateSerializer
        return IDCardSerializer

    def get_queryset(self):
        """
        Filter ID cards based on user's tenant and role.

        - Clerks can only see ID cards they created
        - Kebele leaders can see all ID cards in their kebele
        - Subcity admins can see all ID cards in their subcity and child kebeles
        - City admins can see all ID cards in their city and child subcities/kebeles
        - Super admins can see all ID cards
        """
        queryset = super().get_queryset()
        user = self.request.user

        # If user is superuser or superadmin, return all ID cards
        if user.is_superuser or user.role == 'superadmin':
            return queryset

        # If user has a tenant, filter by tenant hierarchy
        if user.tenant:
            if user.role == 'clerk':
                # Clerks can only see ID cards they created
                queryset = queryset.filter(created_by=user)
            elif user.role == 'kebele_admin':
                # Kebele leaders can see all ID cards in their kebele
                # This is handled automatically by the schema-based multi-tenancy
                pass
            elif user.role == 'subcity_admin':
                # Subcity admins can see all ID cards in their subcity and child kebeles
                # This is handled by the tenant hierarchy and schema-based multi-tenancy
                pass
            elif user.role == 'city_admin':
                # City admins can see all ID cards in their city and child subcities/kebeles
                # This is handled by the tenant hierarchy and schema-based multi-tenancy
                pass

        return queryset

    def perform_create(self, serializer):
        """
        Automatically process citizen photo when creating ID card.
        """
        # Save the ID card first
        id_card = serializer.save(created_by=self.request.user)

        # Process citizen photo if available
        citizen = id_card.citizen
        if citizen and citizen.photo:
            try:
                # Process the citizen's photo with background removal
                result = photo_processor.create_id_card_photo(
                    citizen.photo,
                    style='professional'
                )

                if result['success']:
                    # Update citizen with processed photo
                    citizen.processed_photo = result['processed_image']
                    citizen.photo_processing_metadata = result['metadata']
                    citizen.photo_processing_date = timezone.now()
                    citizen.save()

                    # Update ID card with processed photo
                    id_card.citizen_photo = result['processed_image']
                    id_card.photo_processing_metadata = result['metadata']
                    id_card.save()

                    print(f"✅ Photo processed successfully for ID card {id_card.card_number}")
                else:
                    print(f"⚠️ Photo processing failed for ID card {id_card.card_number}: {result.get('error', 'Unknown error')}")

            except Exception as e:
                print(f"❌ Error processing photo for ID card {id_card.card_number}: {str(e)}")
                # Continue without processed photo - original photo will be used

    @action(detail=True, methods=['post'], permission_classes=[IsKebeleAdminOrHigher])
    def update_status(self, request, pk=None):
        """
        Update the status of an ID card.

        Permissions:
        - Only kebele leaders and higher roles can approve/reject ID cards
        """
        id_card = self.get_object()
        serializer = self.get_serializer(id_card, data=request.data, partial=True)

        if serializer.is_valid():
            # Check permissions based on status change
            new_status = serializer.validated_data.get('status')

            # Save the updated ID card
            id_card = serializer.save()

            # Return the updated ID card
            return Response(IDCardSerializer(id_card).data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], permission_classes=[CanApproveIDCards])
    def approval_action(self, request, pk=None):
        """
        Handle ID card approval workflow actions.

        Actions:
        - submit_for_approval: Clerk submits ID card for approval
        - approve: Kebele leader approves ID card with pattern
        - reject: Kebele leader rejects ID card
        """
        id_card = self.get_object()
        serializer = IDCardApprovalSerializer(data=request.data)

        if serializer.is_valid():
            action = serializer.validated_data['action']
            comment = serializer.validated_data.get('comment', '')
            approval_pattern = serializer.validated_data.get('approval_pattern')

            # Import here to avoid circular imports
            from workflows.models import WorkflowLog, ApprovalAction
            from django.utils import timezone

            old_status = id_card.status

            if action == 'submit_for_approval':
                # Clerks and superadmins can submit for approval, and only draft cards
                if request.user.role not in ['clerk', 'superadmin'] and not request.user.is_superuser:
                    return Response(
                        {'error': 'Only clerks and superadmins can submit ID cards for approval'},
                        status=status.HTTP_403_FORBIDDEN
                    )
                if id_card.status != IDCardStatus.DRAFT:
                    return Response(
                        {'error': 'Only draft ID cards can be submitted for approval'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                id_card.status = IDCardStatus.PENDING_APPROVAL
                id_card.submitted_for_approval_at = timezone.now()
                workflow_action = ApprovalAction.SUBMIT

            elif action == 'approve':
                # Only kebele leaders/admins can approve, and only pending cards
                if request.user.role not in ['kebele_leader', 'kebele_admin', 'subcity_admin', 'superadmin']:
                    return Response(
                        {'error': 'Only kebele leaders and admins can approve ID cards'},
                        status=status.HTTP_403_FORBIDDEN
                    )
                if id_card.status != IDCardStatus.PENDING_APPROVAL:
                    return Response(
                        {'error': 'Only pending ID cards can be approved'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                id_card.status = IDCardStatus.APPROVED
                id_card.approved_by = request.user
                id_card.approved_at = timezone.now()
                id_card.approval_pattern = approval_pattern
                id_card.approval_comment = comment
                workflow_action = ApprovalAction.APPROVE

            elif action == 'reject':
                # Only kebele leaders/admins can reject, and only pending cards
                if request.user.role not in ['kebele_leader', 'kebele_admin', 'subcity_admin', 'superadmin']:
                    return Response(
                        {'error': 'Only kebele leaders and admins can reject ID cards'},
                        status=status.HTTP_403_FORBIDDEN
                    )
                if id_card.status != IDCardStatus.PENDING_APPROVAL:
                    return Response(
                        {'error': 'Only pending ID cards can be rejected'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                id_card.status = IDCardStatus.REJECTED
                id_card.approval_comment = comment
                workflow_action = ApprovalAction.REJECT

            # Save the ID card
            id_card.save()

            # Create workflow log
            WorkflowLog.objects.create(
                id_card=id_card,
                action=workflow_action,
                from_status=old_status,
                to_status=id_card.status,
                comment=comment,
                performed_by=request.user
            )

            # Return updated ID card
            return Response(IDCardSerializer(id_card).data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'], permission_classes=[IsKebeleAdminOrHigher])
    def workflow_logs(self, request, pk=None):
        """
        Get workflow logs for an ID card.

        Permissions:
        - Only kebele leaders and higher roles can view workflow logs
        """
        id_card = self.get_object()
        logs = WorkflowLog.objects.filter(id_card=id_card).order_by('-performed_at')

        from workflows.serializers import WorkflowLogSerializer
        serializer = WorkflowLogSerializer(logs, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsKebeleAdminOrHigher])
    def generate_pdf(self, request, pk=None):
        """
        Generate a PDF for the ID card.

        Permissions:
        - Only kebele leaders and higher roles can generate PDFs
        """
        id_card = self.get_object()

        # Check if ID card is approved
        if id_card.status != IDCardStatus.APPROVED:
            return Response(
                {"detail": "Cannot generate PDF for ID card that is not approved."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(str(id_card.uuid))
        qr.make(fit=True)
        qr_img = qr.make_image(fill_color="black", back_color="white")

        # Save QR code to ID card
        buffer = BytesIO()
        qr_img.save(buffer)
        buffer.seek(0)

        # Save QR code to ID card
        id_card.qr_code.save(f'qr_{id_card.card_number}.png', ContentFile(buffer.getvalue()), save=True)

        # Generate PDF
        pdf_buffer = BytesIO()

        # Create the PDF
        c = canvas.Canvas(pdf_buffer, pagesize=A8)

        # Front side
        c.setFillColor(colors.white)
        c.rect(0, 0, A8[0], A8[1], fill=1)

        # Header
        c.setFillColor(colors.blue)
        c.rect(0, A8[1] - 10*mm, A8[0], 10*mm, fill=1)
        c.setFillColor(colors.white)
        c.setFont("Helvetica-Bold", 8)
        c.drawString(5*mm, A8[1] - 5*mm, "Federal Democratic Republic of Ethiopia")
        c.setFont("Helvetica", 6)
        c.drawString(5*mm, A8[1] - 8*mm, "National ID Card")

        # Citizen details
        c.setFillColor(colors.black)
        c.setFont("Helvetica-Bold", 7)
        c.drawString(30*mm, A8[1] - 15*mm, f"Name: {id_card.citizen.first_name} {id_card.citizen.middle_name}")
        c.drawString(30*mm, A8[1] - 20*mm, f"Last Name: {id_card.citizen.last_name}")
        c.drawString(30*mm, A8[1] - 25*mm, f"DOB: {id_card.citizen.date_of_birth.strftime('%d/%m/%Y')}")
        c.drawString(30*mm, A8[1] - 30*mm, f"Gender: {'Male' if id_card.citizen.gender == 'male' else 'Female'}")
        c.drawString(30*mm, A8[1] - 35*mm, f"Nationality: {id_card.citizen.nationality}")

        # Photo placeholder
        c.setFillColor(colors.lightgrey)
        c.rect(5*mm, A8[1] - 35*mm, 20*mm, 25*mm, fill=1)
        c.setFillColor(colors.black)
        c.setFont("Helvetica", 6)
        c.drawString(10*mm, A8[1] - 25*mm, "PHOTO")

        # QR code
        if id_card.qr_code:
            c.drawImage(id_card.qr_code.path, 5*mm, 5*mm, width=15*mm, height=15*mm)

        # Card number
        c.setFont("Helvetica", 6)
        c.drawString(25*mm, 10*mm, f"Card Number: {id_card.card_number}")

        # Issue and expiry dates - Use Ethiopian calendar with 2-year validity
        from citizens.utils.ethiopian_calendar import (
            gregorian_to_ethiopian,
            ethiopian_to_gregorian,
            format_ethiopian_date,
            add_years_to_ethiopian_date
        )

        now = timezone.now().date()

        # Convert current date to Ethiopian
        current_ethiopian = gregorian_to_ethiopian(now)

        # Add 2 years to Ethiopian date for expiry
        expiry_ethiopian = add_years_to_ethiopian_date(current_ethiopian, 2)

        # Convert back to Gregorian for system storage
        expiry = ethiopian_to_gregorian(expiry_ethiopian['year'], expiry_ethiopian['month'], expiry_ethiopian['day'])

        # Format dates for display (Ethiopian format)
        issue_date_eth = format_ethiopian_date(current_ethiopian, 'DD/MM/YYYY')
        expiry_date_eth = format_ethiopian_date(expiry_ethiopian, 'DD/MM/YYYY')

        c.drawString(25*mm, 7*mm, f"Issue Date (EC): {issue_date_eth}")
        c.drawString(25*mm, 4*mm, f"Expiry Date (EC): {expiry_date_eth}")

        # Save the PDF
        c.showPage()
        c.save()

        # Save PDF to ID card
        pdf_buffer.seek(0)
        id_card.pdf_file.save(f'id_card_{id_card.card_number}.pdf', ContentFile(pdf_buffer.getvalue()), save=True)

        # Update ID card with issue and expiry dates
        id_card.issue_date = now
        id_card.expiry_date = expiry
        id_card.save()

        # Return the updated ID card
        return Response(IDCardSerializer(id_card).data)

    @action(detail=True, methods=['get'], permission_classes=[IsKebeleAdminOrHigher])
    def download_pdf(self, request, pk=None):
        """
        Download the ID card PDF.

        Permissions:
        - Only kebele leaders and higher roles can download PDFs
        """
        id_card = self.get_object()

        if not id_card.pdf_file:
            return Response(
                {"detail": "PDF not generated for this ID card."},
                status=status.HTTP_404_NOT_FOUND
            )

        # Open the file and create a response
        response = HttpResponse(id_card.pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="{id_card.card_number}.pdf"'
        return response

    @action(detail=False, methods=['get'], permission_classes=[IsKebeleAdminOrHigher])
    def statistics(self, request):
        """
        Get statistics about ID cards.

        Permissions:
        - Only kebele leaders and higher roles can view statistics
        """

        total_id_cards = IDCard.objects.count()

        # Status distribution
        status_counts = {}
        for status_choice in IDCardStatus.choices:
            status_code = status_choice[0]
            status_counts[status_code] = IDCard.objects.filter(status=status_code).count()

        # Monthly statistics
        from django.db.models import Count
        from django.db.models.functions import TruncMonth

        monthly_stats = (
            IDCard.objects.annotate(month=TruncMonth('created_at'))
            .values('month')
            .annotate(count=Count('id'))
            .order_by('month')
        )

        monthly_data = [
            {
                'month': item['month'].strftime('%B %Y'),
                'count': item['count']
            }
            for item in monthly_stats
        ]

        return Response({
            'total_id_cards': total_id_cards,
            'status_distribution': status_counts,
            'monthly_statistics': monthly_data
        })

    @action(detail=False, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def process_photo(self, request):
        """
        Process a photo for ID card use with background removal and enhancement.

        Accepts:
        - image: base64 encoded image data or file upload
        - remove_background: boolean (default: True)
        - enhance: boolean (default: True)
        - style: string ('professional', 'passport', 'license') (default: 'professional')

        Returns:
        - processed_image: base64 encoded processed image
        - metadata about processing
        """
        try:
            # Get parameters
            remove_bg = request.data.get('remove_background', True)
            enhance = request.data.get('enhance', True)
            style = request.data.get('style', 'professional')

            # Get image data
            image_data = None

            if 'image' in request.FILES:
                # Handle file upload
                uploaded_file = request.FILES['image']
                image_data = uploaded_file.read()
            elif 'image_data' in request.data:
                # Handle base64 data
                image_base64 = request.data['image_data']
                if image_base64.startswith('data:image'):
                    # Remove data URL prefix
                    image_base64 = image_base64.split(',')[1]
                image_data = base64.b64decode(image_base64)
            else:
                return Response(
                    {'error': 'No image data provided. Use "image" for file upload or "image_data" for base64.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Process the photo
            if style in ['professional', 'passport', 'license']:
                result = photo_processor.create_id_card_photo(image_data, style=style)
            else:
                result = photo_processor.process_id_photo(image_data, remove_bg=remove_bg, enhance=enhance)

            if result['success']:
                return Response({
                    'success': True,
                    'processed_image': f"data:image/png;base64,{result['processed_image']}",
                    'metadata': {
                        'size': result['size'],
                        'background_removed': result['background_removed'],
                        'enhanced': result['enhanced'],
                        'style': result.get('style', 'custom'),
                        'format': result['format']
                    }
                })
            else:
                return Response(
                    {'error': result['error']},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        except Exception as e:
            return Response(
                {'error': f'Photo processing failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def remove_background(self, request):
        """
        Remove background from a photo.

        Accepts:
        - image: base64 encoded image data or file upload

        Returns:
        - processed_image: base64 encoded image with transparent background
        """
        try:
            # Get image data
            image_data = None

            if 'image' in request.FILES:
                uploaded_file = request.FILES['image']
                image_data = uploaded_file.read()
            elif 'image_data' in request.data:
                image_base64 = request.data['image_data']
                if image_base64.startswith('data:image'):
                    image_base64 = image_base64.split(',')[1]
                image_data = base64.b64decode(image_base64)
            else:
                return Response(
                    {'error': 'No image data provided'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Remove background only
            processed_image = photo_processor.remove_background(image_data)

            # Convert to base64
            output_buffer = BytesIO()
            processed_image.save(output_buffer, format='PNG')
            processed_bytes = output_buffer.getvalue()
            processed_base64 = base64.b64encode(processed_bytes).decode('utf-8')

            return Response({
                'success': True,
                'processed_image': f"data:image/png;base64,{processed_base64}",
                'size': processed_image.size,
                'format': 'PNG'
            })

        except Exception as e:
            return Response(
                {'error': f'Background removal failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
