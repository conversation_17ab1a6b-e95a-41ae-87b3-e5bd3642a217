from rest_framework import viewsets, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models import WorkflowLog
from .serializers import WorkflowLogSerializer
from .filters import WorkflowLogFilter


class WorkflowLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing workflow logs.
    """
    queryset = WorkflowLog.objects.all()
    serializer_class = WorkflowLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = WorkflowLogFilter
    search_fields = ['id_card__card_number', 'comment', 'performed_by__username']
    ordering_fields = ['performed_at', 'action']
    ordering = ['-performed_at']
    
    def get_queryset(self):
        """Filter workflow logs based on user's tenant and role."""
        queryset = super().get_queryset()
        user = self.request.user
        
        # If user has a tenant, only show logs from that tenant
        # This is handled automatically by the schema-based multi-tenancy
        
        # Filter by role if needed
        if user.role == 'clerk':
            # Clerks can only see logs for ID cards they created
            queryset = queryset.filter(id_card__created_by=user)
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get statistics about workflow logs."""
        # Only admins can access statistics
        if not (request.user.is_superuser or request.user.role in ['superadmin', 'city_admin', 'subcity_admin', 'kebele_admin']):
            return Response({"detail": "You do not have permission to access statistics."}, status=403)
        
        from django.db.models import Count
        
        # Action distribution
        action_counts = WorkflowLog.objects.values('action').annotate(count=Count('id')).order_by('action')
        
        # User activity
        user_activity = WorkflowLog.objects.values('performed_by__username').annotate(count=Count('id')).order_by('-count')[:10]
        
        # Status transition counts
        status_transitions = WorkflowLog.objects.values('from_status', 'to_status').annotate(count=Count('id')).order_by('-count')
        
        return Response({
            'action_distribution': action_counts,
            'top_users': user_activity,
            'status_transitions': status_transitions
        })
