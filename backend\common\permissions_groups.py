"""
Group-based permission classes that replace hardcoded role-based permissions.
These permissions use the group system instead of checking user.role directly.
"""
from rest_framework import permissions
from django.contrib.auth.models import Permission


class GroupBasedPermission(permissions.BasePermission):
    """
    Base permission class for group-based permissions.
    Checks if user has specific permissions through their groups.
    """
    required_permissions = []  # List of permission codenames required
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Superusers have all permissions
        if user.is_superuser:
            return True
        
        # If no specific permissions required, just check authentication
        if not self.required_permissions:
            return True
        
        # Check if user has any of the required permissions through groups
        for permission_codename in self.required_permissions:
            if user.has_group_permission(permission_codename):
                return True
        
        return False


class CanManageCitizensGroup(GroupBasedPermission):
    """
    Group-based permission for managing citizens.
    Replaces hardcoded role checks with permission-based checks.
    """
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Superusers have all permissions
        if user.is_superuser:
            return True
        
        # For safe methods (GET, HEAD, OPTIONS), check view permission
        if request.method in permissions.SAFE_METHODS:
            return (user.has_group_permission('view_citizen') or 
                   user.has_group_permission('view_citizens') or
                   user.has_group_permission('manage_citizens'))
        
        # For unsafe methods, check specific permissions based on action
        if view.action == 'create':
            return (user.has_group_permission('add_citizen') or 
                   user.has_group_permission('create_citizen') or
                   user.has_group_permission('manage_citizens'))
        
        elif view.action in ['update', 'partial_update']:
            return (user.has_group_permission('change_citizen') or 
                   user.has_group_permission('edit_citizen') or
                   user.has_group_permission('manage_citizens'))
        
        elif view.action == 'destroy':
            return (user.has_group_permission('delete_citizen') or 
                   user.has_group_permission('manage_citizens'))
        
        elif view.action in ['update_status', 'approval_action']:
            return (user.has_group_permission('approve_citizen') or 
                   user.has_group_permission('change_citizen_status') or
                   user.has_group_permission('manage_citizens'))
        
        # Default to checking general citizen management permission
        return user.has_group_permission('manage_citizens')


class CanManageUsersGroup(GroupBasedPermission):
    """
    Group-based permission for managing users.
    Replaces hardcoded role checks with permission-based checks.
    """
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Superusers have all permissions
        if user.is_superuser:
            return True
        
        # For safe methods (GET, HEAD, OPTIONS), check view permission
        if request.method in permissions.SAFE_METHODS:
            return (user.has_group_permission('view_user') or 
                   user.has_group_permission('view_users') or
                   user.has_group_permission('manage_users'))
        
        # For unsafe methods, check specific permissions
        if view.action == 'create':
            return (user.has_group_permission('add_user') or 
                   user.has_group_permission('create_user') or
                   user.has_group_permission('manage_users'))
        
        elif view.action in ['update', 'partial_update']:
            return (user.has_group_permission('change_user') or 
                   user.has_group_permission('edit_user') or
                   user.has_group_permission('manage_users'))
        
        elif view.action == 'destroy':
            return (user.has_group_permission('delete_user') or 
                   user.has_group_permission('manage_users'))
        
        # Default to checking general user management permission
        return user.has_group_permission('manage_users')


class CanManageIDCardsGroup(GroupBasedPermission):
    """
    Group-based permission for managing ID cards.
    """
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Superusers have all permissions
        if user.is_superuser:
            return True
        
        # For safe methods, check view permission
        if request.method in permissions.SAFE_METHODS:
            return (user.has_group_permission('view_idcard') or 
                   user.has_group_permission('view_id_cards') or
                   user.has_group_permission('manage_id_cards'))
        
        # For unsafe methods, check specific permissions
        if view.action == 'create':
            return (user.has_group_permission('add_idcard') or 
                   user.has_group_permission('create_id_card') or
                   user.has_group_permission('print_id_card') or
                   user.has_group_permission('manage_id_cards'))
        
        elif view.action in ['update', 'partial_update']:
            return (user.has_group_permission('change_idcard') or 
                   user.has_group_permission('edit_id_card') or
                   user.has_group_permission('manage_id_cards'))
        
        elif view.action == 'destroy':
            return (user.has_group_permission('delete_idcard') or 
                   user.has_group_permission('manage_id_cards'))
        
        elif view.action in ['approve', 'print']:
            return (user.has_group_permission('approve_id_card') or 
                   user.has_group_permission('print_id_card') or
                   user.has_group_permission('manage_id_cards'))
        
        # Default to checking general ID card management permission
        return user.has_group_permission('manage_id_cards')


class CanManageGroupsPermission(GroupBasedPermission):
    """
    Group-based permission for managing groups and permissions.
    """
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Superusers have all permissions
        if user.is_superuser:
            return True
        
        # Check group management permissions
        return (user.has_group_permission('manage_groups') or 
               user.has_group_permission('manage_permissions') or
               user.has_group_permission('assign_groups') or
               user.has_group_permission('create_groups'))


class IsClerkOrAdminGroup(GroupBasedPermission):
    """
    Group-based replacement for IsClerkOrAdmin.
    Checks if user has basic operational permissions.
    """
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Superusers have all permissions
        if user.is_superuser:
            return True
        
        # Check if user has any basic operational permissions
        basic_permissions = [
            'view_citizen', 'view_citizens', 'manage_citizens',
            'view_user', 'view_users', 'manage_users',
            'view_idcard', 'view_id_cards', 'manage_id_cards',
            'basic_operations'
        ]
        
        for permission in basic_permissions:
            if user.has_group_permission(permission):
                return True
        
        return False


class IsKebeleAdminOrHigherGroup(GroupBasedPermission):
    """
    Group-based replacement for IsKebeleAdminOrHigher.
    Checks if user has administrative permissions.
    """
    
    def has_permission(self, request, view):
        user = request.user
        
        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False
        
        # Superusers have all permissions
        if user.is_superuser:
            return True
        
        # Check if user has administrative permissions
        admin_permissions = [
            'manage_users', 'manage_groups', 'manage_permissions',
            'approve_citizens', 'approve_id_cards', 'admin_operations',
            'kebele_admin', 'subcity_admin', 'city_admin'
        ]
        
        for permission in admin_permissions:
            if user.has_group_permission(permission):
                return True
        
        return False
