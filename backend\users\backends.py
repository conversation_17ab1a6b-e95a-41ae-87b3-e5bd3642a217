from django.contrib.auth import get_user_model
from django.contrib.auth.backends import ModelBackend
from django.db import connection
from django_tenants.utils import schema_context
from tenants.models.tenant import Tenant, Domain

User = get_user_model()


class DomainBasedAuthBackend(ModelBackend):
    """
    Authentication backend that extracts tenant from domain in username.
    Supports usernames like: <EMAIL>
    """

    def authenticate(self, request, username=None, email=None, password=None, **kwargs):
        """
        Authenticate a user based on domain-based username and password.
        Only handles tenant users with domain-based usernames.
        """
        # Only handle username-based authentication (not email)
        if not username or email:
            return None

        # Only handle usernames with @ symbol
        if '@' not in username:
            return None

        user_part, domain_part = username.split('@', 1)

        # Skip system domains (let regular auth handle superadmin)
        system_domains = ['goid.com', 'localhost']
        if domain_part in system_domains:
            return None

        try:
            # Find tenant by domain
            domain_obj = Domain.objects.get(domain=domain_part, is_primary=True)
            tenant = domain_obj.tenant

            # Set tenant context
            with schema_context(tenant.schema_name):
                # Try to find user in tenant schema
                try:
                    user = User.objects.get(username=username)

                    # Check password
                    if user.check_password(password):
                        # Set tenant information on user for later use
                        user._tenant = tenant
                        user._domain = domain_obj

                        # Store the tenant-specific role for JWT token generation
                        # This ensures the user gets the correct role for this tenant
                        user._tenant_role = user.role

                        # Debug logging
                        print(f"🔍 Domain-based Authentication:")
                        print(f"  User: {user.username} ({user.email})")
                        print(f"  Tenant: {tenant.name} (Type: {tenant.type})")
                        print(f"  User's role in this tenant: {user.role}")
                        print(f"  Domain: {domain_part}")

                        return user
                except User.DoesNotExist:
                    pass

        except Domain.DoesNotExist:
            # Domain not found, let other backends handle it
            pass

        return None


class TenantAwareAuthBackend(ModelBackend):
    """
    Authentication backend that respects the tenant context.
    """

    def authenticate(self, request, username=None, email=None, password=None, **kwargs):
        """
        Authenticate a user based on email or username and password.
        """
        if email is None and username is None:
            return None

        try:
            # Try to authenticate by email first
            if email:
                user = User.objects.get(email=email)
            else:
                # Then try by username
                user = User.objects.get(username=username)

            # Check password
            if user.check_password(password):
                # Check if user belongs to the current tenant
                if hasattr(connection, 'tenant') and connection.tenant and user.tenant:
                    # For non-superusers, check if they belong to the current tenant or its hierarchy
                    if not user.is_superuser:
                        current_tenant = connection.tenant
                        user_tenant = user.tenant

                        # Check if user's tenant is the current tenant or in its hierarchy
                        if user_tenant != current_tenant:
                            # Check if current tenant is a child of user's tenant
                            if current_tenant.type == 'subcity' and current_tenant.parent == user_tenant:
                                # User from city can access subcity
                                pass
                            elif current_tenant.type == 'kebele' and current_tenant.parent and current_tenant.parent.parent == user_tenant:
                                # User from city can access kebele
                                pass
                            elif current_tenant.type == 'kebele' and current_tenant.parent == user_tenant:
                                # User from subcity can access kebele
                                pass
                            else:
                                # User doesn't have access to this tenant
                                return None

                return user
        except User.DoesNotExist:
            return None

        return None
