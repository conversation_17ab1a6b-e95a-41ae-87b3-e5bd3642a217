from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from .models import Citizen
from .serializers import CitizenSerializer, CitizenListSerializer
from .filters import Citizen<PERSON>ilter
from common.permissions import CanManageCitizens, IsKebeleAdminOrHigher
from common.permissions_groups import CanManageCitizensGroup, IsKebeleAdminOrHigherGroup


class CitizenViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing citizens.

    Permissions:
    - Clerks can register citizens, view citizens list, and view citizen details
    - Kebele leaders and higher roles can view citizens list and details
    - Only clerks and kebele leaders can edit citizens
    """
    queryset = Citizen.objects.all()
    serializer_class = CitizenSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageCitizensGroup]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = CitizenFilter
    search_fields = ['first_name', 'middle_name', 'last_name', 'phone', 'email']
    ordering_fields = ['created_at', 'first_name', 'last_name', 'date_of_birth']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return different serializers for list and detail views."""
        if self.action == 'list':
            return CitizenListSerializer
        return CitizenSerializer

    def get_queryset(self):
        """Filter citizens based on user's tenant."""
        queryset = super().get_queryset()
        user = self.request.user

        # If user has a tenant, only show citizens from that tenant
        # This is handled automatically by the schema-based multi-tenancy

        return queryset

    def perform_create(self, serializer):
        """
        Automatically process citizen photo when creating citizen.
        """
        # Save the citizen first
        citizen = serializer.save()

        # Process citizen photo if available
        if citizen.photo:
            try:
                # Import photo processor
                from idcards.photo_processing import photo_processor

                # Process the citizen's photo with background removal
                result = photo_processor.create_id_card_photo(
                    citizen.photo,
                    style='professional'
                )

                if result['success']:
                    # Update citizen with processed photo
                    citizen.processed_photo = result['processed_image']
                    citizen.photo_processing_metadata = result['metadata']
                    citizen.photo_processing_date = timezone.now()
                    citizen.save()

                    print(f"✅ Photo processed successfully for citizen {citizen.first_name} {citizen.last_name}")
                else:
                    print(f"⚠️ Photo processing failed for citizen {citizen.first_name} {citizen.last_name}: {result.get('error', 'Unknown error')}")

            except Exception as e:
                print(f"❌ Error processing photo for citizen {citizen.first_name} {citizen.last_name}: {str(e)}")
                # Continue without processed photo - original photo will be used

    @action(detail=True, methods=['get'])
    def id_cards(self, request, pk=None):
        """Get all ID cards for a specific citizen."""
        citizen = self.get_object()
        from idcards.serializers import IDCardSerializer
        id_cards = citizen.id_cards.all()
        serializer = IDCardSerializer(id_cards, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], permission_classes=[IsKebeleAdminOrHigherGroup])
    def statistics(self, request):
        """
        Get statistics about citizens.

        Permissions:
        - Only kebele leaders and higher roles can view reports
        """

        total_citizens = Citizen.objects.count()
        citizens_with_id_cards = Citizen.objects.filter(id_cards__isnull=False).distinct().count()
        citizens_without_id_cards = total_citizens - citizens_with_id_cards

        # Gender distribution
        male_citizens = Citizen.objects.filter(gender='male').count()
        female_citizens = Citizen.objects.filter(gender='female').count()

        # Age groups
        from django.db.models import F, ExpressionWrapper, fields
        from django.utils import timezone
        import datetime

        today = timezone.now().date()

        # Calculate age for each citizen
        citizens_with_age = Citizen.objects.annotate(
            age=ExpressionWrapper(
                (today - F('date_of_birth')) / 365.25,
                output_field=fields.FloatField()
            )
        )

        # Count citizens by age group
        under_18 = citizens_with_age.filter(age__lt=18).count()
        age_18_to_30 = citizens_with_age.filter(age__gte=18, age__lt=30).count()
        age_30_to_50 = citizens_with_age.filter(age__gte=30, age__lt=50).count()
        age_50_plus = citizens_with_age.filter(age__gte=50).count()

        return Response({
            'total_citizens': total_citizens,
            'citizens_with_id_cards': citizens_with_id_cards,
            'citizens_without_id_cards': citizens_without_id_cards,
            'gender_distribution': {
                'male': male_citizens,
                'female': female_citizens
            },
            'age_groups': {
                'under_18': under_18,
                'age_18_to_30': age_18_to_30,
                'age_30_to_50': age_30_to_50,
                'age_50_plus': age_50_plus
            }
        })
