import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  IconButton,
  Tooltip,
  Alert,
  TextField,
  InputAdornment,
  Pagination,
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Search as SearchIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';

const SubcityCitizensList = () => {
  const [citizens, setCitizens] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const { user } = useAuth();
  const navigate = useNavigate();

  const pageSize = 10;

  const getTenantId = () => {
    console.log('🔍 Getting tenant ID...');

    // Get tenant ID from multiple sources for reliability
    let tenantId = user?.tenant_id;
    console.log('🔍 From user context:', tenantId);

    if (!tenantId) {
      // Fallback: Get from localStorage user object
      try {
        const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
        tenantId = storedUser.tenant_id;
        console.log('🔍 From localStorage user:', tenantId);
      } catch (e) {
        console.warn('Could not parse stored user data');
      }
    }

    if (!tenantId) {
      // Fallback: Get from JWT token
      try {
        const accessToken = localStorage.getItem('accessToken');
        if (accessToken) {
          const base64Url = accessToken.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
          const tokenData = JSON.parse(jsonPayload);
          tenantId = tokenData.tenant_id;
          console.log('🔍 From JWT token:', tenantId);
        }
      } catch (e) {
        console.warn('Could not decode JWT token');
      }
    }

    if (!tenantId) {
      console.error('❌ No tenant ID found in any source');
      return null;
    }

    console.log('✅ Final tenant ID:', tenantId);
    return tenantId;
  };

  useEffect(() => {
    fetchCitizens();
  }, [page, search]);

  const fetchCitizens = async () => {
    try {
      setLoading(true);
      const tenantId = getTenantId();

      if (!tenantId) {
        setError('No tenant selected');
        return;
      }

      console.log('🔍 Fetching cross-tenant citizens for subcity tenant:', tenantId);

      const params = new URLSearchParams({
        page: page.toString(),
        page_size: pageSize.toString(),
      });

      if (search.trim()) {
        params.append('search', search.trim());
      }

      const response = await axios.get(`/api/tenants/${tenantId}/citizens/cross_tenant_list/?${params}`);

      console.log('📋 Cross-tenant citizens response:', response.data);
      setCitizens(response.data.results || []);
      setTotalCount(response.data.count || 0);
      setTotalPages(response.data.total_pages || 1);
    } catch (error) {
      console.error('Error fetching cross-tenant citizens:', error);
      setError(error.response?.data?.error || 'Failed to fetch citizens');
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (event) => {
    setSearch(event.target.value);
    setPage(1); // Reset to first page when searching
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleViewCitizen = (citizenId, kebeleId) => {
    // Navigate to the citizen view in the kebele tenant context
    navigate(`/tenants/${kebeleId}/citizens/${citizenId}`);
  };



  const getCitizenName = (citizen) => {
    return [citizen.first_name, citizen.middle_name, citizen.last_name]
      .filter(Boolean)
      .join(' ') || 'Unknown Citizen';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Citizens with Kebele-Approved ID Cards
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Citizens with Kebele-Approved ID Cards ({totalCount})
            </Typography>
            <TextField
              placeholder="Search citizens..."
              value={search}
              onChange={handleSearchChange}
              size="small"
              sx={{ width: 300 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            View citizens whose ID cards have been approved by kebele leaders and are ready for subcity review.
          </Typography>

          {citizens.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No citizens found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {search ? 'Try adjusting your search criteria.' : 'No citizens with kebele-approved ID cards found.'}
              </Typography>
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Citizen</TableCell>
                      <TableCell>Kebele</TableCell>
                      <TableCell>Digital ID</TableCell>
                      <TableCell>Phone</TableCell>
                      <TableCell>Registration Date</TableCell>
                      <TableCell align="center">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {citizens.map((citizen) => (
                      <TableRow key={`${citizen.kebele_tenant.id}-${citizen.id}`} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar
                              sx={{
                                bgcolor: 'primary.main',
                                width: 40,
                                height: 40,
                                fontSize: '1rem'
                              }}
                            >
                              {getCitizenName(citizen)?.charAt(0)?.toUpperCase() || 'U'}
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle2" fontWeight="bold">
                                {getCitizenName(citizen)}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {citizen.gender || 'N/A'} • {citizen.date_of_birth ? new Date().getFullYear() - new Date(citizen.date_of_birth).getFullYear() : 'N/A'} years
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={citizen.kebele_tenant.name}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontFamily="monospace">
                            {citizen.digital_id || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {citizen.phone || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatDate(citizen.created_at)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                            <Tooltip title="View Citizen Details">
                              <IconButton
                                onClick={() => handleViewCitizen(citizen.id, citizen.kebele_tenant.id)}
                                size="small"
                                sx={{
                                  bgcolor: 'primary.main',
                                  color: 'white',
                                  '&:hover': { bgcolor: 'primary.dark' }
                                }}
                              >
                                <PersonIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={handlePageChange}
                    color="primary"
                    showFirstButton
                    showLastButton
                  />
                </Box>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default SubcityCitizensList;
