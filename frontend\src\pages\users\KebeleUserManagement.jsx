import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Menu,
  Tabs,
  Tab,
  Autocomplete,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormGroup,
  FormControlLabel,
  Checkbox,
  FormLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Business as BusinessIcon,
  PersonAdd as PersonAddIcon,
  People as PeopleIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Home as HomeIcon,
  Lock as LockIcon,
  <PERSON><PERSON>ert as MoreVertIcon,
  Security as SecurityIcon,
  Assignment as AssignmentIcon,
  VpnKey as VpnKeyIcon,
  Add as AddIcon,
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  Person as PersonIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../hooks/usePermissions';

const KebeleUserManagement = () => {
  const { user } = useAuth();
  const { hasPermission } = usePermissions();
  const navigate = useNavigate();

  // State management
  const [kebeles, setKebeles] = useState([]);
  const [selectedKebele, setSelectedKebele] = useState(null);
  const [kebeleUsers, setKebeleUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [tabValue, setTabValue] = useState(0); // 0: Users, 1: Groups, 2: Group Lists

  // User management dialogs
  const [createUserDialogOpen, setCreateUserDialogOpen] = useState(false);
  const [editUserDialogOpen, setEditUserDialogOpen] = useState(false);
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);
  const [creating, setCreating] = useState(false);
  const [editing, setEditing] = useState(false);
  const [changingPassword, setChangingPassword] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [passwordUser, setPasswordUser] = useState(null);
  const [userMenuAnchor, setUserMenuAnchor] = useState(null);
  const [selectedUserForMenu, setSelectedUserForMenu] = useState(null);

  // Group management state
  const [tenantGroups, setTenantGroups] = useState([]);
  const [groupTemplates, setGroupTemplates] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [groupsWithUsers, setGroupsWithUsers] = useState([]);
  const [selectedUserDetails, setSelectedUserDetails] = useState(null);
  const [userDetailsDialogOpen, setUserDetailsDialogOpen] = useState(false);
  const [groupDetailsDialogOpen, setGroupDetailsDialogOpen] = useState(false);
  const [selectedGroupForManagement, setSelectedGroupForManagement] = useState(null);
  const [groupPermissions, setGroupPermissions] = useState([]);
  const [groupUsers, setGroupUsers] = useState([]);
  const [createGroupDialogOpen, setCreateGroupDialogOpen] = useState(false);
  const [assignGroupDialogOpen, setAssignGroupDialogOpen] = useState(false);
  const [grantPermissionDialogOpen, setGrantPermissionDialogOpen] = useState(false);
  const [editGroupDialogOpen, setEditGroupDialogOpen] = useState(false);
  const [editingGroup, setEditingGroup] = useState(null);
  const [groupLoading, setGroupLoading] = useState(false);
  const [apiStatus, setApiStatus] = useState('unknown'); // 'connected', 'disconnected', 'unknown'

  // Form state
  const [newUser, setNewUser] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    role: 'clerk', // Keep for backward compatibility
    primary_group_id: '',
    password: '',
    phone_number: ''
  });

  const [editUser, setEditUser] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    role: 'clerk', // Keep for backward compatibility
    primary_group_id: '',
    phone_number: ''
  });

  const [passwordForm, setPasswordForm] = useState({
    new_password: '',
    confirm_password: ''
  });

  // Group management form state
  const [groupForm, setGroupForm] = useState({
    // Group creation
    group_name: '',
    description: '',
    group_type: 'custom',
    level: 10,
    permission_codenames: [],
    template_id: '',
    // Group assignment
    user_email: '',
    user_emails: [], // For multiple user selection
    group_id: '',
    is_primary: false,
    // Permission granting
    permission_codename: '',
    reason: ''
  });

  // Form validation state
  const [formErrors, setFormErrors] = useState({
    group_name: false,
    description: false,
    user_email: false,
    group_id: false,
    permission_codename: false
  });

  useEffect(() => {
    fetchKebeles();
  }, []);

  useEffect(() => {
    if (selectedKebele) {
      fetchKebeleUsers(selectedKebele.id);
      fetchTenantGroups(selectedKebele.id);
    }
  }, [selectedKebele]);

  useEffect(() => {
    fetchPermissions();
    fetchGroupTemplates();
    testApiConnectivity();
  }, []);

  const testApiConnectivity = async () => {
    try {
      // Test a simple endpoint to check connectivity
      await axios.get('/api/auth/permissions/');
      setApiStatus('connected');
    } catch (err) {
      if (err.response?.status >= 400 && err.response?.status < 500) {
        // 4xx errors mean the API is reachable but requires auth
        setApiStatus('connected');
      } else {
        // 5xx errors or network errors mean API issues
        setApiStatus('disconnected');
      }
    }
  };

  const fetchKebeles = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch kebeles under the current subcity
      const response = await axios.get('/api/tenants/', {
        params: {
          type: 'kebele',
          parent: user.tenant?.id
        }
      });

      setKebeles(response.data.results || []);
    } catch (error) {
      console.error('Failed to fetch kebeles:', error);
      setError('Failed to load kebeles. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchKebeleUsers = async (kebeleId) => {
    try {
      const response = await axios.get(`/api/tenants/${kebeleId}/users/`);
      console.log('🔍 Fetched kebele users:', response.data);
      // Log group information for debugging
      if (response.data && response.data.length > 0) {
        response.data.forEach(user => {
          console.log(`User ${user.email}:`, {
            primary_group_id: user.primary_group_id,
            primary_group_name: user.primary_group_name,
            groups: user.groups
          });
        });
      }
      setKebeleUsers(response.data || []);
    } catch (error) {
      console.error('Failed to fetch kebele users:', error);
      setKebeleUsers([]);
    }
  };

  const handleCreateUser = async () => {
    if (!selectedKebele) return;

    try {
      setCreating(true);

      // Create username with domain if not already present
      let username = newUser.username;

      // Get the kebele's domain from domains array or construct it
      let kebeleDomain = selectedKebele.primary_domain;
      if (!kebeleDomain && selectedKebele.schema_name) {
        kebeleDomain = `${selectedKebele.schema_name}.goid.local`;
      }

      if (kebeleDomain && !username.includes('@')) {
        username = `${username}@${kebeleDomain}`;
      }

      const userData = {
        ...newUser,
        username,
        password2: newUser.password, // Add password confirmation
        tenant: selectedKebele.id
      };

      console.log('🔍 Creating user with data:', userData);
      console.log('🔍 Target kebele:', selectedKebele);

      await axios.post(`/api/tenants/${selectedKebele.id}/create_user/`, userData);

      // Refresh users list
      fetchKebeleUsers(selectedKebele.id);

      // Reset form and close dialog
      setNewUser({
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        role: 'clerk',
        primary_group_id: '',
        password: '',
        phone_number: ''
      });
      setCreateUserDialogOpen(false);

    } catch (error) {
      console.error('Failed to create user:', error);
      console.error('Error response:', error.response?.data);

      // Handle validation errors
      if (error.response?.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'object' && !errorData.detail) {
          // Format validation errors
          const errorMessages = Object.entries(errorData)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('\n');
          setError(`Validation errors:\n${errorMessages}`);
        } else {
          setError(errorData.detail || 'Failed to create user. Please try again.');
        }
      } else {
        setError('Failed to create user. Please try again.');
      }
    } finally {
      setCreating(false);
    }
  };

  const handleInputChange = (field, value) => {
    setNewUser(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditInputChange = (field, value) => {
    setEditUser(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
    setEditUser({
      username: user.username,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      role: user.role,
      primary_group_id: user.primary_group_id || '',
      phone_number: user.phone_number || ''
    });
    setEditUserDialogOpen(true);
  };

  const handleUpdateUser = async () => {
    if (!selectedKebele || !editingUser) return;

    try {
      setEditing(true);

      const updateData = {
        user_id: editingUser.id,
        ...editUser
      };

      console.log('🔍 Updating user with data:', updateData);
      console.log('🔍 Target kebele:', selectedKebele);

      await axios.patch(`/api/tenants/${selectedKebele.id}/update_user/`, updateData);

      // Refresh users list
      fetchKebeleUsers(selectedKebele.id);

      // Close dialog and reset state
      setEditUserDialogOpen(false);
      setEditingUser(null);
      setEditUser({
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        role: 'clerk',
        primary_group_id: '',
        phone_number: ''
      });

    } catch (error) {
      console.error('Failed to update user:', error);
      console.error('Error response:', error.response?.data);

      // Handle validation errors
      if (error.response?.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'object' && !errorData.detail) {
          // Format validation errors
          const errorMessages = Object.entries(errorData)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('\n');
          setError(`Validation errors:\n${errorMessages}`);
        } else {
          setError(errorData.detail || 'Failed to update user. Please try again.');
        }
      } else {
        setError('Failed to update user. Please try again.');
      }
    } finally {
      setEditing(false);
    }
  };

  const handlePasswordInputChange = (field, value) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleChangePassword = (user) => {
    setPasswordUser(user);
    setPasswordForm({
      new_password: '',
      confirm_password: ''
    });
    setPasswordDialogOpen(true);
    setUserMenuAnchor(null);
  };

  const handleUpdatePassword = async () => {
    if (!selectedKebele || !passwordUser) return;

    if (passwordForm.new_password !== passwordForm.confirm_password) {
      setError('Passwords do not match');
      return;
    }

    if (passwordForm.new_password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    try {
      setChangingPassword(true);

      const passwordData = {
        user_id: passwordUser.id,
        new_password: passwordForm.new_password,
        confirm_password: passwordForm.confirm_password
      };

      console.log('🔍 Changing password for user:', passwordUser.email);
      console.log('🔍 Target kebele:', selectedKebele);

      await axios.patch(`/api/tenants/${selectedKebele.id}/change_user_password/`, passwordData);

      // Close dialog and reset state
      setPasswordDialogOpen(false);
      setPasswordUser(null);
      setPasswordForm({
        new_password: '',
        confirm_password: ''
      });

      // Show success message
      setError(''); // Clear any previous errors
      // You could add a success snackbar here

    } catch (error) {
      console.error('Failed to change password:', error);
      console.error('Error response:', error.response?.data);

      // Handle validation errors
      if (error.response?.data) {
        const errorData = error.response.data;
        setError(errorData.detail || 'Failed to change password. Please try again.');
      } else {
        setError('Failed to change password. Please try again.');
      }
    } finally {
      setChangingPassword(false);
    }
  };

  const handleUserMenuOpen = (event, user) => {
    setUserMenuAnchor(event.currentTarget);
    setSelectedUserForMenu(user);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
    setSelectedUserForMenu(null);
  };

  // Group Management Functions
  const fetchTenantGroups = async (tenantId) => {
    try {
      const response = await axios.get(`/api/auth/group-management/tenant_groups/?tenant_id=${tenantId}`);
      setTenantGroups(response.data.groups || []);
    } catch (err) {
      console.error('Failed to fetch tenant groups:', err);
      // Fallback to mock data for testing
      const mockGroups = [
        {
          id: 1,
          name: 'Kebele Clerks',
          description: 'Basic clerk operations',
          group_type: 'operational',
          level: 10,
          users_count: 3,
          permissions_count: 5,
          tenant: { name: selectedKebele?.name }
        },
        {
          id: 2,
          name: 'Kebele Leaders',
          description: 'Leadership and management',
          group_type: 'administrative',
          level: 30,
          users_count: 1,
          permissions_count: 9,
          tenant: { name: selectedKebele?.name }
        }
      ];
      setTenantGroups(mockGroups);
    }
  };

  const fetchGroupTemplates = async () => {
    try {
      const response = await axios.get('/api/auth/group-management/group_templates/');
      setGroupTemplates(response.data.templates || []);
    } catch (err) {
      console.error('Failed to fetch group templates:', err);
      // Fallback to mock data for testing
      setGroupTemplates([
        { id: 1, name: 'Kebele Clerk', description: 'Basic clerk permissions', group_type: 'operational', level: 10, permissions_count: 5 },
        { id: 2, name: 'Kebele Leader', description: 'Leadership permissions', group_type: 'administrative', level: 30, permissions_count: 9 },
        { id: 3, name: 'ID Card Processor', description: 'ID card processing', group_type: 'functional', level: 20, permissions_count: 4 },
        { id: 4, name: 'Emergency Coordinator', description: 'Emergency response', group_type: 'functional', level: 50, permissions_count: 7 },
        { id: 5, name: 'Data Entry Specialist', description: 'Data entry operations', group_type: 'operational', level: 15, permissions_count: 5 },
        { id: 6, name: 'Supervisor', description: 'Supervisory permissions', group_type: 'administrative', level: 40, permissions_count: 10 }
      ]);
    }
  };

  const fetchPermissions = async () => {
    try {
      // Try the group management available_permissions endpoint first
      const response = await axios.get('/api/auth/group-management/available_permissions/');
      console.log('Available permissions from group management:', response.data);

      // Deduplicate permissions by codename to avoid duplicate keys
      const rawPermissions = response.data.permissions || [];
      const uniquePermissions = rawPermissions.filter((permission, index, self) =>
        index === self.findIndex(p => p.codename === permission.codename)
      );
      setPermissions(uniquePermissions);
    } catch (err) {
      console.error('Failed to fetch permissions from group management:', err);
      // Fallback to the regular permissions endpoint
      try {
        const fallbackResponse = await axios.get('/api/auth/permissions/');
        console.log('Fallback permissions:', fallbackResponse.data);

        // Deduplicate fallback permissions too
        const rawFallbackPermissions = fallbackResponse.data.results || fallbackResponse.data || [];
        const uniqueFallbackPermissions = rawFallbackPermissions.filter((permission, index, self) =>
          index === self.findIndex(p => p.codename === permission.codename)
        );
        setPermissions(uniqueFallbackPermissions);
      } catch (fallbackErr) {
        console.error('Failed to fetch permissions from fallback:', fallbackErr);
        // Use logical, general permissions that make sense - already unique by design
        setPermissions([
          // Citizen Management - General permissions that cover all citizen operations
          { id: 1, codename: 'view_citizens', name: 'View Citizens', content_type: 'Citizens', description: 'Can view citizen records and all related information (spouse, children, parents, emergency contacts)' },
          { id: 2, codename: 'create_citizens', name: 'Create Citizens', content_type: 'Citizens', description: 'Can register new citizens and add all related information (spouse, children, parents, emergency contacts)' },
          { id: 3, codename: 'edit_citizens', name: 'Edit Citizens', content_type: 'Citizens', description: 'Can modify citizen information and all related data (spouse, children, parents, emergency contacts)' },
          { id: 4, codename: 'delete_citizens', name: 'Delete Citizens', content_type: 'Citizens', description: 'Can delete citizen records and all related information' },

          // ID Card Management
          { id: 5, codename: 'view_id_cards', name: 'View ID Cards', content_type: 'ID Cards', description: 'Can view ID card applications and status' },
          { id: 6, codename: 'create_id_cards', name: 'Create ID Cards', content_type: 'ID Cards', description: 'Can create new ID card applications' },
          { id: 7, codename: 'edit_id_cards', name: 'Edit ID Cards', content_type: 'ID Cards', description: 'Can modify ID card applications' },
          { id: 8, codename: 'approve_id_cards', name: 'Approve ID Cards', content_type: 'ID Cards', description: 'Can approve or reject ID card applications' },
          { id: 9, codename: 'print_id_cards', name: 'Print ID Cards', content_type: 'ID Cards', description: 'Can print approved ID cards' },
          { id: 10, codename: 'issue_id_cards', name: 'Issue ID Cards', content_type: 'ID Cards', description: 'Can issue and distribute ID cards to citizens' },

          // User Management
          { id: 11, codename: 'view_users', name: 'View Users', content_type: 'User Management', description: 'Can view user accounts and their details' },
          { id: 12, codename: 'create_users', name: 'Create Users', content_type: 'User Management', description: 'Can create new user accounts' },
          { id: 13, codename: 'edit_users', name: 'Edit Users', content_type: 'User Management', description: 'Can modify user account information' },
          { id: 14, codename: 'delete_users', name: 'Delete Users', content_type: 'User Management', description: 'Can delete user accounts' },

          // System Administration
          { id: 15, codename: 'manage_permissions', name: 'Manage Permissions', content_type: 'System', description: 'Can assign and manage user permissions and roles' },
          { id: 16, codename: 'view_reports', name: 'View Reports', content_type: 'System', description: 'Can view system reports and analytics' },
          { id: 17, codename: 'export_data', name: 'Export Data', content_type: 'System', description: 'Can export data from the system' },
          { id: 18, codename: 'view_system_logs', name: 'View System Logs', content_type: 'System', description: 'Can view system logs and audit trails' }
        ]);
      }
    }
  };

  const fetchGroupsWithUsers = async (tenantId) => {
    try {
      setGroupLoading(true);
      const response = await axios.get(`/api/auth/group-management/groups_with_users/?tenant_id=${tenantId}`);
      setGroupsWithUsers(response.data.groups || []);
    } catch (err) {
      console.error('Failed to fetch groups with users:', err);
      // Fallback to mock data for testing
      setGroupsWithUsers([
        {
          id: 1,
          name: 'Kebele Clerks',
          description: 'Basic clerk operations for kebele',
          group_type: 'operational',
          level: 10,
          users: [
            { id: 1, email: '<EMAIL>', full_name: 'John Doe', tenant: { name: 'Kebele 1-A' } },
            { id: 2, email: '<EMAIL>', full_name: 'Jane Smith', tenant: { name: 'Kebele 1-A' } }
          ],
          users_count: 2,
          permissions_count: 5,
          tenant: { name: selectedKebele?.name }
        },
        {
          id: 2,
          name: 'Kebele Leaders',
          description: 'Leadership and management for kebele',
          group_type: 'administrative',
          level: 30,
          users: [
            { id: 3, email: '<EMAIL>', full_name: 'Ahmed Hassan', tenant: { name: 'Kebele 1-A' } }
          ],
          users_count: 1,
          permissions_count: 9,
          tenant: { name: selectedKebele?.name }
        }
      ]);
    } finally {
      setGroupLoading(false);
    }
  };

  const fetchUserDetails = async (userId) => {
    try {
      setGroupLoading(true);
      const response = await axios.get(`/api/auth/group-management/user_groups_details/?user_id=${userId}`);
      setSelectedUserDetails(response.data);
      setUserDetailsDialogOpen(true);
    } catch (err) {
      console.error('Failed to fetch user details:', err);
      setError('Failed to load user details. Please try again.');
    } finally {
      setGroupLoading(false);
    }
  };

  const handleViewGroupDetails = async (group) => {
    try {
      setGroupLoading(true);
      // Fetch detailed group information including users and permissions
      const response = await axios.get(`/api/auth/group-management/group_details/?group_id=${group.id}`);
      setSelectedUserDetails({
        group: group,
        users: response.data.users || [],
        permissions: response.data.permissions || [],
        ...response.data
      });
      setUserDetailsDialogOpen(true);
    } catch (err) {
      console.error('Failed to fetch group details:', err);
      // Fallback to showing basic group info
      setSelectedUserDetails({
        group: group,
        users: [],
        permissions: [],
        message: 'Could not load detailed group information. API may not be available.'
      });
      setUserDetailsDialogOpen(true);
    } finally {
      setGroupLoading(false);
    }
  };

  const fetchGroupPermissions = async (groupId) => {
    try {
      // Use the existing groups_with_users endpoint to get group details including permissions
      const response = await axios.get('/api/auth/group-management/groups_with_users/', {
        params: { tenant_id: selectedKebele?.id }
      });

      // Find the specific group and get its permissions
      const groups = response.data.groups || [];
      const targetGroup = groups.find(g => g.id === groupId);

      if (targetGroup && targetGroup.permissions) {
        setGroupPermissions(targetGroup.permissions);
      } else {
        setGroupPermissions([]);
      }
      setApiStatus('connected');
    } catch (err) {
      console.error('Failed to fetch group permissions:', err);
      setApiStatus('disconnected');
      setGroupPermissions([]);
    }
  };

  const fetchGroupUsers = async (groupId) => {
    try {
      // Use the existing groups_with_users endpoint to get group details including users
      const response = await axios.get('/api/auth/group-management/groups_with_users/', {
        params: { tenant_id: selectedKebele?.id }
      });

      // Find the specific group and get its users
      const groups = response.data.groups || [];
      const targetGroup = groups.find(g => g.id === groupId);

      if (targetGroup && targetGroup.users) {
        setGroupUsers(targetGroup.users);
      } else {
        // Fallback: filter kebele users by their groups
        const usersInGroup = kebeleUsers.filter(user =>
          user.groups && user.groups.some(group => group.id === groupId)
        );
        setGroupUsers(usersInGroup);
      }
      setApiStatus('connected');
    } catch (err) {
      console.error('Failed to fetch group users:', err);
      setApiStatus('disconnected');
      setGroupUsers([]);
    }
  };

  const handleManageGroupPermissions = async (group) => {
    setSelectedGroupForManagement(group);
    setGroupForm({ ...groupForm, group_id: group.id.toString() });
    await fetchGroupPermissions(group.id);
    setGrantPermissionDialogOpen(true);
  };

  const handleManageGroupUsers = async (group) => {
    setSelectedGroupForManagement(group);
    setGroupForm({ ...groupForm, group_id: group.id.toString() });
    await fetchGroupUsers(group.id);
    setAssignGroupDialogOpen(true);
  };

  const handlePermissionToggle = async (permission, isChecked) => {
    if (!selectedGroupForManagement) return;

    try {
      setGroupLoading(true);

      if (isChecked) {
        // Use the existing manage_group endpoint to assign permissions
        await axios.post('/api/auth/group-management/manage_group/', {
          action: 'assign_permissions',
          group_id: parseInt(selectedGroupForManagement.id),
          permission_codenames: [permission.codename]
        });
      } else {
        // Use the existing manage_group endpoint to remove permissions
        await axios.post('/api/auth/group-management/manage_group/', {
          action: 'remove_permissions',
          group_id: parseInt(selectedGroupForManagement.id),
          permission_codenames: [permission.codename]
        });
      }

      // Update local state after successful API call
      if (isChecked) {
        setGroupPermissions(prev => {
          if (prev.some(p => p.codename === permission.codename)) {
            return prev; // Already exists
          }
          return [...prev, permission];
        });
      } else {
        setGroupPermissions(prev => prev.filter(p => p.codename !== permission.codename));
      }

      // Show success message
      setSuccess(`Permission ${isChecked ? 'added to' : 'removed from'} ${selectedGroupForManagement.name}`);

      // Refresh tenant groups to update counts
      if (selectedKebele) {
        try {
          await fetchTenantGroups(selectedKebele.id);
        } catch (err) {
          console.log('Could not refresh tenant groups');
        }
      }

      setApiStatus('connected');
    } catch (err) {
      console.error('Failed to toggle permission:', err);
      console.error('Error response:', err.response?.data);
      setApiStatus('disconnected');

      // Show detailed error message
      const errorMessage = err.response?.data?.error ||
                          err.response?.data?.detail ||
                          Object.values(err.response?.data || {}).join(', ') ||
                          `Failed to ${isChecked ? 'add' : 'remove'} permission. Please try again.`;
      setError(errorMessage);
    } finally {
      setGroupLoading(false);
    }
  };

  const handleSaveGroupPermissions = () => {
    // Close the dialog - changes are saved in real-time
    setGrantPermissionDialogOpen(false);
    setSelectedGroupForManagement(null);
    setGroupPermissions([]);
  };

  const handleRemoveUserFromGroup = async (user) => {
    if (!selectedGroupForManagement) return;

    try {
      setGroupLoading(true);

      // Use the existing manage_group endpoint to remove user from group
      await axios.post('/api/auth/group-management/manage_group/', {
        action: 'remove_user_from_group',
        user_email: user.email,
        group_id: parseInt(selectedGroupForManagement.id)
      });

      // Update local state
      setGroupUsers(prev => prev.filter(u => u.id !== user.id));

      // Show success message
      setSuccess(`${user.first_name} ${user.last_name} removed from ${selectedGroupForManagement.name}`);

      // Refresh tenant groups to update counts
      if (selectedKebele) {
        fetchTenantGroups(selectedKebele.id);
      }

      // Refresh kebele users to update their group assignments
      fetchKebeleUsers(selectedKebele.id);

      setApiStatus('connected');
    } catch (err) {
      console.error('Failed to remove user from group:', err);
      console.error('Error response:', err.response?.data);
      setApiStatus('disconnected');

      const errorMessage = err.response?.data?.error ||
                          err.response?.data?.detail ||
                          Object.values(err.response?.data || {}).join(', ') ||
                          'Please try again.';
      setError(`Failed to remove user from group. ${errorMessage}`);
    } finally {
      setGroupLoading(false);
    }
  };

  const validateGroupForm = () => {
    const errors = {
      group_name: !groupForm.group_name.trim(),
      description: false,
      user_email: false,
      group_id: false,
      permission_codename: false
    };

    setFormErrors(errors);
    return !Object.values(errors).some(error => error);
  };

  const handleCreateGroup = async () => {
    if (!validateGroupForm() || !selectedKebele) {
      setError('Please fill in required fields');
      return;
    }

    setGroupLoading(true);
    try {
      let groupData;

      if (groupForm.template_id) {
        // Create from template
        groupData = {
          action: 'create_from_template',
          template_id: parseInt(groupForm.template_id),
          group_name: groupForm.group_name,
          tenant_id: selectedKebele.id
        };
      } else {
        // Create custom group
        groupData = {
          action: 'create_group',
          group_name: groupForm.group_name,
          description: groupForm.description,
          group_type: groupForm.group_type,
          level: groupForm.level,
          tenant_id: selectedKebele.id
        };
      }

      console.log('🔍 Creating group with data:', groupData);
      console.log('🔍 Group form state:', groupForm);

      const response = await axios.post('/api/auth/group-management/manage_group/', groupData);

      // Assign permissions if specified and not using template
      if (!groupForm.template_id && groupForm.permission_codenames.length > 0 && response.data.group?.id) {
        try {
          await handleAssignPermissionsToGroup(response.data.group.id, groupForm.permission_codenames);
        } catch (permError) {
          console.warn('Failed to assign additional permissions:', permError);
          // Don't fail the whole operation if permission assignment fails
        }
      }

      setCreateGroupDialogOpen(false);
      resetGroupForm();
      fetchTenantGroups(selectedKebele.id);
      setError('');
      setSuccess(response.data.message || 'Group created successfully!');
    } catch (err) {
      console.error('Failed to create group:', err);
      let errorMessage = 'Failed to create group';

      if (err.response?.status === 500) {
        errorMessage = 'Server error: Please check if the backend server is running and the database is properly configured.';
      } else if (err.response?.status === 404) {
        errorMessage = 'API endpoint not found. Please check if the group management API is properly configured.';
      } else if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      } else if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err.response?.data) {
        // Show the full error response for debugging
        console.log('Full error response:', err.response.data);
        errorMessage = `Server error: ${JSON.stringify(err.response.data)}`;
      } else if (err.message) {
        errorMessage = `Network error: ${err.message}`;
      }

      setError(errorMessage);
    } finally {
      setGroupLoading(false);
    }
  };

  const handleAssignPermissionsToGroup = async (groupId, permissionCodenames) => {
    try {
      await axios.post('/api/auth/group-management/manage_group/', {
        action: 'assign_permissions',
        group_id: groupId,
        permission_codenames: permissionCodenames
      });
    } catch (err) {
      console.error('Failed to assign permissions to group:', err);
      throw err;
    }
  };

  const handleAssignGroup = async () => {
    const usersToAssign = groupForm.user_emails.length > 0 ? groupForm.user_emails : [groupForm.user_email];

    if (usersToAssign.length === 0 || !usersToAssign[0] || !groupForm.group_id || !selectedKebele) {
      setError('Please select at least one user and a group');
      return;
    }

    setGroupLoading(true);
    try {
      // Process multiple users
      const assignmentPromises = usersToAssign.map(async (userEmail) => {
        try {
          // Use the existing manage_group endpoint to add user to group
          await axios.post('/api/auth/group-management/manage_group/', {
            action: 'add_user_to_group',
            user_email: userEmail,
            group_id: parseInt(groupForm.group_id),
            is_primary: groupForm.is_primary
          });

          setApiStatus('connected');
          return { success: true, email: userEmail };
        } catch (apiErr) {
          console.error(`Failed to assign ${userEmail} to group:`, apiErr);
          console.error('Error response:', apiErr.response?.data);
          setApiStatus('disconnected');

          const errorMessage = apiErr.response?.data?.error ||
                              apiErr.response?.data?.detail ||
                              Object.values(apiErr.response?.data || {}).join(', ') ||
                              apiErr.message;
          return { success: false, email: userEmail, error: errorMessage };
        }
      });

      const results = await Promise.all(assignmentPromises);
      const successCount = results.filter(r => r.success).length;

      // Update local state if we have a selected group for management
      if (selectedGroupForManagement) {
        const newUsers = kebeleUsers.filter(user => usersToAssign.includes(user.email));
        setGroupUsers(prev => [...prev, ...newUsers]);
      }

      setAssignGroupDialogOpen(false);
      resetGroupForm();
      fetchKebeleUsers(selectedKebele.id);
      setError('');

      const failedCount = results.filter(r => !r.success).length;
      if (successCount === usersToAssign.length) {
        setSuccess(`${successCount} user${successCount > 1 ? 's' : ''} assigned to group successfully!`);
      } else if (successCount > 0) {
        setSuccess(`${successCount} of ${usersToAssign.length} users assigned successfully.`);
        if (failedCount > 0) {
          setError(`${failedCount} user${failedCount > 1 ? 's' : ''} could not be assigned. Please try again.`);
        }
      } else {
        setError('No users could be assigned to the group. Please check the API configuration.');
      }
    } catch (err) {
      console.error('Failed to assign group:', err);
      let errorMessage = 'Failed to assign users to group';

      if (err.response?.status === 500) {
        errorMessage = 'Server error: Please check if the backend server is running and properly configured.';
      } else if (err.response?.status === 404) {
        errorMessage = 'API endpoint not found. Please verify the group management API configuration.';
      } else if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      } else if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      }

      setError(errorMessage);
    } finally {
      setGroupLoading(false);
    }
  };

  const handleGrantPermission = async () => {
    if (!groupForm.group_id || !groupForm.permission_codename) {
      setError('Please select a group and permission');
      return;
    }

    setGroupLoading(true);
    try {
      await axios.post('/api/auth/group-management/manage_group/', {
        action: 'assign_permissions',
        group_id: parseInt(groupForm.group_id),
        permission_codenames: [groupForm.permission_codename]
      });

      setGrantPermissionDialogOpen(false);
      resetGroupForm();
      fetchTenantGroups(selectedKebele.id);
      setError('');
      setSuccess('Permission granted to group successfully!');
    } catch (err) {
      console.error('Failed to grant permission:', err);
      let errorMessage = 'Failed to grant permission';

      if (err.response?.status === 500) {
        errorMessage = 'Server error: Please check if the backend server is running and properly configured.';
      } else if (err.response?.status === 404) {
        errorMessage = 'API endpoint not found. Please verify the group management API configuration.';
      } else if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      } else if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      }

      setError(errorMessage);
    } finally {
      setGroupLoading(false);
    }
  };

  const resetGroupForm = () => {
    setGroupForm({
      group_name: '',
      description: '',
      group_type: 'custom',
      level: 10,
      permission_codenames: [],
      template_id: '',
      user_email: '',
      user_emails: [],
      group_id: '',
      is_primary: false,
      permission_codename: '',
      reason: ''
    });
    setFormErrors({
      group_name: false,
      description: false,
      user_email: false,
      group_id: false,
      permission_codename: false
    });
  };

  const openCreateGroupDialog = () => {
    resetGroupForm();
    setCreateGroupDialogOpen(true);
  };

  const openAssignGroupDialog = () => {
    resetGroupForm();
    setAssignGroupDialogOpen(true);
  };

  const openGrantPermissionDialog = () => {
    resetGroupForm();
    setGrantPermissionDialogOpen(true);
  };

  const handleDeleteGroup = async (group) => {
    if (!group || group.is_system_group) {
      setError('System groups cannot be deleted');
      return;
    }

    if (!window.confirm(`Are you sure you want to delete the group "${group.name}"? This action cannot be undone.`)) {
      return;
    }

    setGroupLoading(true);
    try {
      await axios.post('/api/auth/group-management/manage_group/', {
        action: 'delete_group',
        group_id: parseInt(group.id)
      });

      setSuccess(`Group "${group.name}" deleted successfully`);
      await fetchTenantGroups();
      await fetchGroupsWithUsers();
    } catch (err) {
      console.error('Failed to delete group:', err);
      let errorMessage = 'Failed to delete group';

      if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      }

      setError(errorMessage);
    } finally {
      setGroupLoading(false);
    }
  };

  const openEditGroupDialog = (group) => {
    if (group.is_system_group) {
      setError('System groups cannot be edited');
      return;
    }

    setEditingGroup(group);
    setGroupForm({
      ...groupForm,
      group_name: group.name,
      description: group.description,
      group_type: group.group_type,
      level: group.level
    });
    setEditGroupDialogOpen(true);
  };

  const handleEditGroup = async () => {
    if (!validateGroupForm() || !editingGroup) {
      setError('Please fill in required fields');
      return;
    }

    setGroupLoading(true);
    try {
      await axios.post('/api/auth/group-management/manage_group/', {
        action: 'edit_group',
        group_id: parseInt(editingGroup.id),
        group_name: groupForm.group_name,
        description: groupForm.description,
        group_type: groupForm.group_type,
        level: parseInt(groupForm.level)
      });

      setSuccess(`Group "${groupForm.group_name}" updated successfully`);
      setEditGroupDialogOpen(false);
      resetGroupForm();
      setEditingGroup(null);
      await fetchTenantGroups();
      await fetchGroupsWithUsers();
    } catch (err) {
      console.error('Failed to edit group:', err);
      let errorMessage = 'Failed to edit group';

      if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      }

      setError(errorMessage);
    } finally {
      setGroupLoading(false);
    }
  };

  // Helper function to filter roles appropriate for kebele users
  const filterKebeleRoles = (roles) => {
    return roles.filter(role => {
      // Allow kebele-specific roles
      const kebeleRoles = ['clerk', 'kebele_leader'];

      // Allow legacy kebele roles
      if (kebeleRoles.includes(role.codename)) {
        return true;
      }

      // Allow custom roles created specifically for kebeles (not global high-level roles)
      if (role.type === 'dynamic' || role.type === 'custom') {
        // Exclude high-level administrative roles
        const excludedRoles = ['superadmin', 'city_admin', 'subcity_admin'];
        if (excludedRoles.includes(role.codename)) {
          return false;
        }

        // Include roles that are either:
        // 1. Not global (tenant-specific)
        // 2. Have appropriate level (not too high)
        return !role.is_global || (role.level && role.level <= 60);
      }

      // Exclude other high-level roles
      const excludedRoles = ['superadmin', 'city_admin', 'subcity_admin', 'kebele_admin'];
      return !excludedRoles.includes(role.codename);
    });
  };

  // Enhanced permission categorization with better organization
  const groupPermissionsByCategory = (permissions) => {
    const categoryMapping = {
      // Citizens management
      'citizens': { name: 'Citizens Management', icon: '👥', priority: 1 },
      'citizen': { name: 'Citizens Management', icon: '👥', priority: 1 },

      // ID Cards
      'id_cards': { name: 'ID Cards', icon: '🆔', priority: 2 },
      'id_card': { name: 'ID Cards', icon: '🆔', priority: 2 },
      'cards': { name: 'ID Cards', icon: '🆔', priority: 2 },

      // User management
      'users': { name: 'User Management', icon: '👤', priority: 3 },
      'user': { name: 'User Management', icon: '👤', priority: 3 },

      // Permissions and roles
      'permissions': { name: 'Permissions & Roles', icon: '🔐', priority: 4 },
      'permission': { name: 'Permissions & Roles', icon: '🔐', priority: 4 },
      'roles': { name: 'Permissions & Roles', icon: '🔐', priority: 4 },
      'role': { name: 'Permissions & Roles', icon: '🔐', priority: 4 },

      // Reports and analytics
      'reports': { name: 'Reports & Analytics', icon: '📊', priority: 5 },
      'report': { name: 'Reports & Analytics', icon: '📊', priority: 5 },
      'analytics': { name: 'Reports & Analytics', icon: '📊', priority: 5 },

      // System administration
      'admin': { name: 'System Administration', icon: '⚙️', priority: 6 },
      'system': { name: 'System Administration', icon: '⚙️', priority: 6 },
      'settings': { name: 'System Administration', icon: '⚙️', priority: 6 },

      // Default
      'other': { name: 'Other Permissions', icon: '📋', priority: 7 }
    };

    const groups = permissions.reduce((acc, permission) => {
      let category = 'other';
      let categoryInfo = categoryMapping.other;

      if (permission.category) {
        category = permission.category.toLowerCase();
        categoryInfo = categoryMapping[category] || categoryMapping.other;
      } else if (permission.codename) {
        // Extract category from codename with better logic
        const parts = permission.codename.split('_');
        if (parts.length > 1) {
          const potentialCategory = parts.slice(1).join('_').toLowerCase();

          // Check for exact matches first
          if (categoryMapping[potentialCategory]) {
            category = potentialCategory;
            categoryInfo = categoryMapping[potentialCategory];
          } else {
            // Check for partial matches
            const foundCategory = Object.keys(categoryMapping).find(key =>
              potentialCategory.includes(key) || key.includes(potentialCategory)
            );
            if (foundCategory) {
              category = foundCategory;
              categoryInfo = categoryMapping[foundCategory];
            }
          }
        }
      }

      const groupKey = categoryInfo.name;
      if (!acc[groupKey]) {
        acc[groupKey] = {
          permissions: [],
          icon: categoryInfo.icon,
          priority: categoryInfo.priority
        };
      }
      acc[groupKey].permissions.push(permission);
      return acc;
    }, {});

    // Sort groups by priority
    const sortedGroups = Object.entries(groups).sort(([, a], [, b]) => a.priority - b.priority);
    return Object.fromEntries(sortedGroups);
  };

  const getKebeleTypeChip = (type) => {
    return (
      <Chip
        icon={<HomeIcon />}
        label="Kebele"
        color="success"
        size="small"
        variant="outlined"
      />
    );
  };

  const getRoleChip = (role) => {
    const colors = {
      clerk: 'primary',
      kebele_leader: 'secondary',
      kebele_admin: 'warning'
    };

    return (
      <Chip
        label={role.replace('_', ' ').toUpperCase()}
        color={colors[role] || 'default'}
        size="small"
      />
    );
  };

  if (!hasPermission('manage_users')) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          You don't have permission to manage users.
        </Alert>
      </Box>
    );
  }

  if (user?.role !== 'subcity_admin' && user?.role !== 'superadmin' && !user?.is_superuser) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info">
          This page is only available for subcity administrators and superadmins.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Kebele User Management
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Manage users for kebeles under {user.tenant?.name}
            </Typography>
          </Box>

          {/* API Status Indicator */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="caption" color="textSecondary">
              API Status:
            </Typography>
            <Chip
              size="small"
              label={apiStatus === 'connected' ? 'Connected' : apiStatus === 'disconnected' ? 'Disconnected' : 'Checking...'}
              color={apiStatus === 'connected' ? 'success' : apiStatus === 'disconnected' ? 'error' : 'default'}
              variant={apiStatus === 'unknown' ? 'outlined' : 'filled'}
            />
            {apiStatus === 'disconnected' && (
              <Tooltip title="Click to retry connection">
                <IconButton size="small" onClick={testApiConnectivity}>
                  <SecurityIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>
      </Box>

      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          action={
            error.includes('Server error') ? (
              <Button
                color="inherit"
                size="small"
                onClick={() => setError('')}
              >
                Dismiss
              </Button>
            ) : null
          }
        >
          <Box>
            <Typography variant="body2" sx={{ mb: 1 }}>
              {error}
            </Typography>
            {error.includes('Server error') && (
              <Typography variant="caption" color="textSecondary">
                <strong>Troubleshooting:</strong><br/>
                1. Check if Django server is running: <code>python manage.py runserver</code><br/>
                2. Verify database migrations: <code>python manage.py migrate</code><br/>
                3. Setup permissions: <code>python manage.py setup_permissions</code><br/>
                4. Setup roles: <code>python manage.py setup_dynamic_roles</code>
              </Typography>
            )}
          </Box>
        </Alert>
      )}

      {success && (
        <Alert
          severity="success"
          sx={{ mb: 3 }}
          action={
            <IconButton
              color="inherit"
              size="small"
              onClick={() => setSuccess('')}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
        >
          {success}
        </Alert>
      )}

      {/* Top Section - Kebele Selection */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <BusinessIcon sx={{ mr: 1 }} />
            Select Kebele ({kebeles.length})
          </Typography>
          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
            Choose a kebele to manage its users and groups
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : kebeles.length === 0 ? (
            <Alert severity="info">
              No kebeles found under this subcity.
            </Alert>
          ) : (
            <Grid container spacing={2}>
              {kebeles.map((kebele) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={kebele.id}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      border: selectedKebele?.id === kebele.id ? 2 : 1,
                      borderColor: selectedKebele?.id === kebele.id ? 'primary.main' : 'divider',
                      backgroundColor: selectedKebele?.id === kebele.id ? 'primary.light' : 'background.paper',
                      '&:hover': {
                        borderColor: 'primary.main',
                        backgroundColor: selectedKebele?.id === kebele.id ? 'primary.main' : 'action.hover',
                      },
                      transition: 'all 0.2s ease-in-out'
                    }}
                    onClick={() => setSelectedKebele(kebele)}
                  >
                    <CardContent sx={{ textAlign: 'center', py: 2 }}>
                      <BusinessIcon
                        sx={{
                          fontSize: 40,
                          mb: 1,
                          color: selectedKebele?.id === kebele.id ? 'primary.contrastText' : 'primary.main'
                        }}
                      />
                      <Typography
                        variant="h6"
                        sx={{
                          color: selectedKebele?.id === kebele.id ? 'primary.contrastText' : 'text.primary',
                          fontSize: '1rem',
                          fontWeight: 600
                        }}
                      >
                        {kebele.name}
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{
                          color: selectedKebele?.id === kebele.id ? 'primary.contrastText' : 'text.secondary',
                          display: 'block'
                        }}
                      >
                        {kebele.type} • {kebele.users_count || 0} users
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{
                          color: selectedKebele?.id === kebele.id ? 'primary.contrastText' : 'text.secondary',
                          display: 'block',
                          fontSize: '0.7rem'
                        }}
                      >
                        Schema: {kebele.schema_name}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </CardContent>
      </Card>

      {/* Main Content - User and Group Management */}
      {!selectedKebele ? (
        <Card>
          <CardContent sx={{ py: 8, textAlign: 'center' }}>
            <BusinessIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" color="textSecondary" gutterBottom>
              Select a Kebele
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Choose a kebele from above to view and manage its users and groups.
            </Typography>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center' }}>
                <PeopleIcon sx={{ mr: 1 }} />
                {selectedKebele.name} Management
              </Typography>
            </Box>
            <Divider sx={{ mb: 3 }} />

            {/* Tabs for Users and Groups */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
              <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                <Tab label="Users" />
                <Tab label="Groups" />
              </Tabs>
            </Box>
            {/* Users Tab */}
            {tabValue === 0 && (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6">
                    Users ({kebeleUsers.length})
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<PersonAddIcon />}
                    onClick={() => setCreateUserDialogOpen(true)}
                  >
                    Add User
                  </Button>
                </Box>

                {kebeleUsers.length === 0 ? (
                  <Box sx={{ py: 8, textAlign: 'center' }}>
                    <PeopleIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="h6" color="textSecondary" gutterBottom>
                      No Users Found
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      No users found for this kebele. Click "Add User" to create the first user.
                    </Typography>
                  </Box>
                ) : (
                  <Grid container spacing={2}>
                    {kebeleUsers.map((user) => (
                      <Grid item xs={12} sm={6} md={4} key={user.id}>
                        <Card variant="outlined" sx={{ height: '100%' }}>
                          <CardContent>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                              <Avatar sx={{ mr: 2 }}>
                                {user.first_name?.charAt(0) || user.username?.charAt(0)}
                              </Avatar>
                              <Box sx={{ flexGrow: 1 }}>
                                <Typography variant="subtitle1" fontWeight="medium">
                                  {`${user.first_name} ${user.last_name}`.trim() || user.username}
                                </Typography>
                                <Typography variant="body2" color="textSecondary">
                                  {user.email}
                                </Typography>
                              </Box>
                              <IconButton size="small" onClick={(e) => handleUserMenuOpen(e, user)}>
                                <MoreVertIcon />
                              </IconButton>
                            </Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              {getRoleChip(user.role)}
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {user.groups && user.groups.length > 0 ? (
                                  user.groups.map((group, index) => (
                                    <Chip
                                      key={group.id}
                                      label={group.name}
                                      size="small"
                                      variant={group.is_primary ? "filled" : "outlined"}
                                      color={group.is_primary ? "primary" : "default"}
                                      sx={{ fontSize: '0.7rem', height: '20px' }}
                                    />
                                  ))
                                ) : (
                                  <Typography variant="caption" color="textSecondary">
                                    No groups
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                )}
              </Box>
            )}

            {/* Groups Tab */}
            {tabValue === 1 && (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6">
                    Group Management
                  </Typography>
                </Box>

                {/* Group Management Actions */}
                <Grid container spacing={3} sx={{ mb: 4 }}>
                  <Grid item xs={12} sm={4}>
                    <Card sx={{ height: '100%', border: 1, borderColor: 'divider' }}>
                      <CardContent sx={{ textAlign: 'center', py: 3 }}>
                        <AddIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                        <Typography variant="h6" gutterBottom>
                          Create Group
                        </Typography>
                        <Typography variant="body2" color="textSecondary" paragraph>
                          Create a group for this kebele from templates or custom permissions.
                        </Typography>
                        <Button
                          variant="contained"
                          fullWidth
                          onClick={openCreateGroupDialog}
                        >
                          Create Group
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Card sx={{ height: '100%', border: 1, borderColor: 'divider' }}>
                      <CardContent sx={{ textAlign: 'center', py: 3 }}>
                        <AssignmentIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                        <Typography variant="h6" gutterBottom>
                          Assign to Group
                        </Typography>
                        <Typography variant="body2" color="textSecondary" paragraph>
                          Assign users to existing groups in this kebele.
                        </Typography>
                        <Button
                          variant="contained"
                          fullWidth
                          onClick={openAssignGroupDialog}
                        >
                          Assign to Group
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Card sx={{ height: '100%', border: 1, borderColor: 'divider' }}>
                      <CardContent sx={{ textAlign: 'center', py: 3 }}>
                        <VpnKeyIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                        <Typography variant="h6" gutterBottom>
                          Grant Permission
                        </Typography>
                        <Typography variant="body2" color="textSecondary" paragraph>
                          Grant additional permissions to groups.
                        </Typography>
                        <Button
                          variant="contained"
                          fullWidth
                          onClick={openGrantPermissionDialog}
                        >
                          Grant Permission
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                {/* Available Groups */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Available Groups for {selectedKebele.name}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Groups created for this kebele. Click on a group to manage users and permissions.
                  </Typography>
                </Box>
                <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Group Name</TableCell>
                            <TableCell>Type</TableCell>
                            <TableCell>Level</TableCell>
                            <TableCell>Users</TableCell>
                            <TableCell>Permissions</TableCell>
                            <TableCell align="center">Actions</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {tenantGroups.length === 0 ? (
                            <TableRow>
                              <TableCell colSpan={6} align="center">
                                <Typography variant="body2" color="textSecondary">
                                  No groups available. Create groups from templates or check API connection.
                                </Typography>
                              </TableCell>
                            </TableRow>
                          ) : (
                            tenantGroups.map((group) => (
                              <TableRow key={group.id} hover>
                                <TableCell>
                                  <Box>
                                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                      {group.name}
                                    </Typography>
                                    <Typography variant="caption" color="textSecondary">
                                      {group.description}
                                    </Typography>
                                  </Box>
                                </TableCell>
                                <TableCell>
                                  <Chip
                                    label={group.group_type}
                                    size="small"
                                    color={group.group_type === 'administrative' ? 'success' : group.group_type === 'functional' ? 'warning' : 'info'}
                                  />
                                </TableCell>
                                <TableCell>
                                  <Typography variant="body2">{group.level}</Typography>
                                </TableCell>
                                <TableCell>
                                  <Typography variant="body2">{group.users_count || 0}</Typography>
                                </TableCell>
                                <TableCell>
                                  <Typography variant="body2">{group.permissions_count || 0}</Typography>
                                </TableCell>
                                <TableCell align="center">
                                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                                    <Tooltip title="Manage Users">
                                      <IconButton
                                        size="small"
                                        onClick={() => handleManageGroupUsers(group)}
                                      >
                                        <PersonAddIcon fontSize="small" />
                                      </IconButton>
                                    </Tooltip>
                                    <Tooltip title={group.is_system_group ? "System groups permissions cannot be modified" : "Manage Permissions"}>
                                      <span>
                                        <IconButton
                                          size="small"
                                          onClick={() => handleManageGroupPermissions(group)}
                                          disabled={group.is_system_group}
                                        >
                                          <VpnKeyIcon fontSize="small" />
                                        </IconButton>
                                      </span>
                                    </Tooltip>
                                    <Tooltip title="View Details">
                                      <IconButton
                                        size="small"
                                        onClick={() => handleViewGroupDetails(group)}
                                      >
                                        <VisibilityIcon fontSize="small" />
                                      </IconButton>
                                    </Tooltip>
                                    {!group.is_system_group && (
                                      <>
                                        <Tooltip title="Edit Group">
                                          <IconButton
                                            size="small"
                                            onClick={() => openEditGroupDialog(group)}
                                          >
                                            <EditIcon fontSize="small" />
                                          </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Delete Group">
                                          <IconButton
                                            size="small"
                                            onClick={() => handleDeleteGroup(group)}
                                            color="error"
                                          >
                                            <DeleteIcon fontSize="small" />
                                          </IconButton>
                                        </Tooltip>
                                      </>
                                    )}
                                  </Box>
                                </TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                </TableContainer>
              </Box>
            )}
          </CardContent>
        </Card>
      )}

      {/* User Actions Menu */}
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={handleUserMenuClose}
      >
        <MenuItem onClick={() => {
          handleEditUser(selectedUserForMenu);
          handleUserMenuClose();
        }}>
          <EditIcon sx={{ mr: 1 }} />
          Edit User
        </MenuItem>
        <MenuItem onClick={() => {
          handleChangePassword(selectedUserForMenu);
          handleUserMenuClose();
        }}>
          <LockIcon sx={{ mr: 1 }} />
          Change Password
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => {
          setGroupForm({ ...groupForm, user_email: selectedUserForMenu?.email || '' });
          openAssignGroupDialog();
          handleUserMenuClose();
        }}>
          <AssignmentIcon sx={{ mr: 1 }} />
          Assign to Group
        </MenuItem>
      </Menu>

      {/* Create User Dialog */}
      <Dialog open={createUserDialogOpen} onClose={() => setCreateUserDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Create User for {selectedKebele?.name}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Username"
                value={newUser.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                helperText={`Will be: ${newUser.username}@${selectedKebele?.primary_domain || selectedKebele?.schema_name + '.goid.local' || 'domain.com'}`}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={newUser.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={newUser.first_name}
                onChange={(e) => handleInputChange('first_name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={newUser.last_name}
                onChange={(e) => handleInputChange('last_name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Primary Group (Optional)</InputLabel>
                <Select
                  value={newUser.primary_group_id || ''}
                  label="Primary Group (Optional)"
                  onChange={(e) => handleInputChange('primary_group_id', e.target.value)}
                >
                  <MenuItem value="">
                    <em>No Primary Group</em>
                  </MenuItem>
                  {tenantGroups.map((group) => (
                    <MenuItem key={group.id} value={group.id}>
                      <Box>
                        <Typography variant="body2">{group.name}</Typography>
                        <Typography variant="caption" color="textSecondary">
                          {group.group_type} • Level {group.level} • {group.users_count} users
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Typography variant="caption" color="textSecondary" sx={{ mt: 0.5, display: 'block' }}>
                Select a primary group for the user. Additional groups can be assigned after user creation.
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={newUser.phone_number}
                onChange={(e) => handleInputChange('phone_number', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Password"
                type="password"
                value={newUser.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateUserDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateUser}
            variant="contained"
            disabled={creating || !newUser.username || !newUser.password}
            startIcon={creating ? <CircularProgress size={20} /> : <PersonAddIcon />}
          >
            {creating ? 'Creating...' : 'Create User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={editUserDialogOpen} onClose={() => setEditUserDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Edit User: {editingUser?.first_name} {editingUser?.last_name}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Username"
                value={editUser.username}
                onChange={(e) => handleEditInputChange('username', e.target.value)}
                helperText="Username cannot be changed after creation"
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={editUser.email}
                onChange={(e) => handleEditInputChange('email', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={editUser.first_name}
                onChange={(e) => handleEditInputChange('first_name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={editUser.last_name}
                onChange={(e) => handleEditInputChange('last_name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Primary Group</InputLabel>
                <Select
                  value={editUser.primary_group_id || ''}
                  label="Primary Group"
                  onChange={(e) => handleEditInputChange('primary_group_id', e.target.value)}
                >
                  <MenuItem value="">
                    <em>No Primary Group</em>
                  </MenuItem>
                  {tenantGroups.map((group) => (
                    <MenuItem key={group.id} value={group.id}>
                      <Box>
                        <Typography variant="body2">{group.name}</Typography>
                        <Typography variant="caption" color="textSecondary">
                          {group.group_type} • Level {group.level} • {group.users_count} users
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Typography variant="caption" color="textSecondary" sx={{ mt: 0.5, display: 'block' }}>
                Change the user's primary group. Use group management for additional group assignments.
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={editUser.phone_number}
                onChange={(e) => handleEditInputChange('phone_number', e.target.value)}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditUserDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleUpdateUser}
            variant="contained"
            disabled={editing || !editUser.email || !editUser.first_name || !editUser.last_name}
            startIcon={editing ? <CircularProgress size={20} /> : <EditIcon />}
          >
            {editing ? 'Updating...' : 'Update User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Change Password Dialog */}
      <Dialog open={passwordDialogOpen} onClose={() => setPasswordDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Change Password for {passwordUser?.first_name} {passwordUser?.last_name}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="New Password"
                type="password"
                value={passwordForm.new_password}
                onChange={(e) => handlePasswordInputChange('new_password', e.target.value)}
                helperText="Password must be at least 8 characters long"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Confirm New Password"
                type="password"
                value={passwordForm.confirm_password}
                onChange={(e) => handlePasswordInputChange('confirm_password', e.target.value)}
                error={Boolean(passwordForm.confirm_password && passwordForm.new_password !== passwordForm.confirm_password)}
                helperText={
                  passwordForm.confirm_password && passwordForm.new_password !== passwordForm.confirm_password
                    ? "Passwords do not match"
                    : "Re-enter the new password"
                }
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPasswordDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleUpdatePassword}
            variant="contained"
            disabled={
              changingPassword ||
              !passwordForm.new_password ||
              !passwordForm.confirm_password ||
              passwordForm.new_password !== passwordForm.confirm_password ||
              passwordForm.new_password.length < 8
            }
            startIcon={changingPassword ? <CircularProgress size={20} /> : <LockIcon />}
          >
            {changingPassword ? 'Changing...' : 'Change Password'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Group Dialog */}
      <Dialog open={createGroupDialogOpen} onClose={() => setCreateGroupDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create Group for {selectedKebele?.name}</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              This group will be specific to <strong>{selectedKebele?.name}</strong> and can only be assigned to users in this kebele.
              You can create a group from a template or create a custom group with specific permissions.
            </Typography>
          </Alert>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {/* Template Selection */}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Create From Template (Optional)</InputLabel>
                <Select
                  value={groupForm.template_id}
                  onChange={(e) => {
                    const templateId = e.target.value;
                    setGroupForm({ ...groupForm, template_id: templateId });

                    // If template selected, auto-fill some fields and clear manual permissions
                    if (templateId) {
                      const template = groupTemplates.find(t => t.id === parseInt(templateId));
                      if (template) {
                        setGroupForm(prev => ({
                          ...prev,
                          template_id: templateId,
                          description: template.description,
                          group_type: template.group_type,
                          level: template.level,
                          permission_codenames: [] // Clear manual permissions when using template
                        }));
                      }
                    } else {
                      // If no template, reset to custom values
                      setGroupForm(prev => ({
                        ...prev,
                        template_id: '',
                        description: '',
                        group_type: 'custom',
                        level: 10
                      }));
                    }
                  }}
                  label="Create From Template (Optional)"
                >
                  <MenuItem value="">
                    <em>Create Custom Group</em>
                  </MenuItem>
                  {groupTemplates.map((template) => (
                    <MenuItem key={template.id} value={template.id}>
                      <Box>
                        <Typography variant="body2">{template.name}</Typography>
                        <Typography variant="caption" color="textSecondary">
                          {template.group_type} • Level {template.level} • {template.permissions_count} permissions
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
                <Typography variant="caption" color="textSecondary" sx={{ mt: 0.5 }}>
                  Select a template for quick setup, or leave empty to create a custom group
                </Typography>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Group Name *"
                value={groupForm.group_name}
                onChange={(e) => {
                  setGroupForm({ ...groupForm, group_name: e.target.value });
                  if (formErrors.group_name) {
                    setFormErrors({ ...formErrors, group_name: false });
                  }
                }}
                error={formErrors.group_name}
                helperText={formErrors.group_name ? "Group name is required" : "Display name (e.g., 'Kebele 1-A Clerks')"}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={groupForm.description}
                onChange={(e) => setGroupForm({ ...groupForm, description: e.target.value })}
                helperText="Detailed description of the group's responsibilities"
                disabled={!!groupForm.template_id}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Group Type</InputLabel>
                <Select
                  value={groupForm.group_type}
                  onChange={(e) => setGroupForm({ ...groupForm, group_type: e.target.value })}
                  label="Group Type"
                  disabled={!!groupForm.template_id}
                >
                  <MenuItem value="operational">Operational</MenuItem>
                  <MenuItem value="administrative">Administrative</MenuItem>
                  <MenuItem value="functional">Functional</MenuItem>
                  <MenuItem value="custom">Custom</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Hierarchy Level"
                type="number"
                value={groupForm.level}
                onChange={(e) => setGroupForm({ ...groupForm, level: parseInt(e.target.value) })}
                helperText="1-100 (higher = more authority)"
                inputProps={{ min: 1, max: 100 }}
                disabled={!!groupForm.template_id}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl component="fieldset" fullWidth>
                <FormLabel component="legend">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="subtitle1">
                      Permissions {groupForm.template_id && '(From Template)'}
                    </Typography>
                    <Chip
                      size="small"
                      label={`${groupForm.permission_codenames.length} selected`}
                      color={groupForm.permission_codenames.length > 0 ? 'primary' : 'default'}
                    />
                  </Box>
                </FormLabel>

                {groupForm.template_id && (
                  <Alert severity="info" sx={{ mt: 1, mb: 2 }}>
                    <Typography variant="body2">
                      Permissions will be automatically assigned from the selected template.
                      You can modify them after group creation if needed.
                    </Typography>
                  </Alert>
                )}

                {/* Quick Actions */}
                {!groupForm.template_id && (
                  <Box sx={{ mt: 1, mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => setGroupForm({ ...groupForm, permission_codenames: [] })}
                      disabled={groupForm.permission_codenames.length === 0}
                    >
                      Clear All
                    </Button>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => {
                        const basicPermissions = permissions
                          .filter(p => ['view_citizen', 'add_citizen', 'view_idcard'].includes(p.codename))
                          .map(p => p.codename);
                        setGroupForm({ ...groupForm, permission_codenames: basicPermissions });
                      }}
                    >
                      Basic Permissions
                    </Button>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => {
                        const allPermissions = permissions.map(p => p.codename);
                        setGroupForm({ ...groupForm, permission_codenames: allPermissions });
                      }}
                    >
                      Select All
                    </Button>
                  </Box>
                )}

                {!groupForm.template_id && (
                  <Box sx={{ maxHeight: 400, overflowY: 'auto', border: 1, borderColor: 'divider', borderRadius: 1 }}>
                    {Object.entries(groupPermissionsByCategory(permissions)).map(([categoryName, categoryData]) => (
                      <Accordion key={categoryName} defaultExpanded={categoryData.priority <= 3}>
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon />}
                          sx={{
                            backgroundColor: 'action.hover',
                            '&:hover': { backgroundColor: 'action.selected' }
                          }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                            <Typography variant="h6" sx={{ fontSize: '1rem' }}>
                              {categoryData.icon} {categoryName}
                            </Typography>
                            <Chip
                              size="small"
                              label={categoryData.permissions.length}
                              color="default"
                              variant="outlined"
                            />
                            <Box sx={{ flexGrow: 1 }} />
                            <Chip
                              size="small"
                              label={`${categoryData.permissions.filter(p => groupForm.permission_codenames.includes(p.codename)).length} selected`}
                              color={categoryData.permissions.some(p => groupForm.permission_codenames.includes(p.codename)) ? 'success' : 'default'}
                              variant="filled"
                            />
                          </Box>
                        </AccordionSummary>
                        <AccordionDetails sx={{ pt: 0 }}>
                          {/* Category Actions */}
                          <Box sx={{ mb: 2, display: 'flex', gap: 1 }}>
                            <Button
                              size="small"
                              variant="text"
                              onClick={() => {
                                const categoryPermissions = categoryData.permissions.map(p => p.codename);
                                const newPermissions = [...new Set([...groupForm.permission_codenames, ...categoryPermissions])];
                                setGroupForm({ ...groupForm, permission_codenames: newPermissions });
                              }}
                              disabled={categoryData.permissions.every(p => groupForm.permission_codenames.includes(p.codename))}
                            >
                              Select All in Category
                            </Button>
                            <Button
                              size="small"
                              variant="text"
                              onClick={() => {
                                const categoryPermissions = categoryData.permissions.map(p => p.codename);
                                const newPermissions = groupForm.permission_codenames.filter(p => !categoryPermissions.includes(p));
                                setGroupForm({ ...groupForm, permission_codenames: newPermissions });
                              }}
                              disabled={!categoryData.permissions.some(p => groupForm.permission_codenames.includes(p.codename))}
                            >
                              Clear Category
                            </Button>
                          </Box>

                          <FormGroup>
                            {categoryData.permissions.map((permission) => (
                              <FormControlLabel
                                key={permission.codename}
                                control={
                                  <Checkbox
                                    checked={groupForm.permission_codenames.includes(permission.codename)}
                                    onChange={(e) => {
                                      const newPermissions = e.target.checked
                                        ? [...groupForm.permission_codenames, permission.codename]
                                        : groupForm.permission_codenames.filter(p => p !== permission.codename);
                                      setGroupForm({ ...groupForm, permission_codenames: newPermissions });
                                    }}
                                    color="primary"
                                  />
                                }
                              label={
                                <Box sx={{ py: 0.5 }}>
                                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                    {permission.name}
                                  </Typography>
                                  <Typography variant="caption" color="textSecondary" sx={{ display: 'block' }}>
                                    {permission.description || permission.codename}
                                  </Typography>
                                  <Chip
                                    size="small"
                                    label={permission.codename}
                                    variant="outlined"
                                    sx={{ mt: 0.5, fontSize: '0.7rem', height: 20 }}
                                  />
                                </Box>
                              }
                              sx={{
                                alignItems: 'flex-start',
                                mb: 1,
                                p: 1,
                                borderRadius: 1,
                                '&:hover': { backgroundColor: 'action.hover' }
                              }}
                            />
                          ))}
                        </FormGroup>
                        </AccordionDetails>
                      </Accordion>
                    ))}
                  </Box>
                )}

                {/* Summary */}
                <Box sx={{ mt: 2, p: 2, backgroundColor: 'action.hover', borderRadius: 1 }}>
                  <Typography variant="body2" color="textSecondary">
                    <strong>Selected Permissions Summary:</strong> {groupForm.permission_codenames.length} permissions selected
                    {groupForm.permission_codenames.length > 0 && (
                      <Box component="span" sx={{ ml: 1 }}>
                        ({groupForm.permission_codenames.slice(0, 3).join(', ')}
                        {groupForm.permission_codenames.length > 3 && `, +${groupForm.permission_codenames.length - 3} more`})
                      </Box>
                    )}
                  </Typography>
                  {groupForm.template_id && (
                    <Typography variant="caption" color="primary" sx={{ display: 'block', mt: 1 }}>
                      Note: Template permissions will be automatically assigned when the group is created.
                    </Typography>
                  )}
                </Box>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateGroupDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleCreateGroup}
            variant="contained"
            disabled={groupLoading || !groupForm.group_name}
            startIcon={groupLoading ? <CircularProgress size={20} /> : <SecurityIcon />}
          >
            {groupLoading ? 'Creating...' : (groupForm.template_id ? 'Create from Template' : 'Create Group')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Manage Group Users Dialog */}
      <Dialog
        open={assignGroupDialogOpen}
        onClose={() => setAssignGroupDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Manage Users: {selectedGroupForManagement?.name || 'Assign User to Group'}
            </Typography>
            <IconButton onClick={() => setAssignGroupDialogOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedGroupForManagement ? (
            <Box>
              {/* Group Information */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                    <AssignmentIcon sx={{ mr: 1 }} />
                    Group Information
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Group Name</Typography>
                      <Typography variant="body1">{selectedGroupForManagement.name}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Type</Typography>
                      <Chip
                        label={selectedGroupForManagement.group_type}
                        size="small"
                        color={selectedGroupForManagement.group_type === 'administrative' ? 'success' : selectedGroupForManagement.group_type === 'functional' ? 'warning' : 'info'}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Level</Typography>
                      <Typography variant="body1">{selectedGroupForManagement.level}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Current Users</Typography>
                      <Typography variant="body1">{groupUsers.length}</Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Current Users in Group */}
              {groupUsers.length > 0 && (
                <Card sx={{ mb: 3 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <PeopleIcon sx={{ mr: 1 }} />
                      Current Users ({groupUsers.length})
                    </Typography>
                    <Grid container spacing={2}>
                      {groupUsers.map((user) => (
                        <Grid item xs={12} sm={6} key={user.id}>
                          <Card variant="outlined">
                            <CardContent sx={{ py: 2 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Avatar sx={{ mr: 2, width: 32, height: 32 }}>
                                    {user.first_name?.charAt(0) || user.username?.charAt(0)}
                                  </Avatar>
                                  <Box>
                                    <Typography variant="body2" fontWeight="medium">
                                      {`${user.first_name} ${user.last_name}`.trim() || user.username}
                                    </Typography>
                                    <Typography variant="caption" color="textSecondary">
                                      {user.email}
                                    </Typography>
                                  </Box>
                                </Box>
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => handleRemoveUserFromGroup(user)}
                                  title="Remove from group"
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Box>
                            </CardContent>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  </CardContent>
                </Card>
              )}

              {/* Add New User to Group */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                    <PersonAddIcon sx={{ mr: 1 }} />
                    Add Users to {selectedGroupForManagement.name}
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Autocomplete
                        multiple
                        options={kebeleUsers.filter(user => !groupUsers.some(gu => gu.id === user.id))}
                        getOptionLabel={(option) => `${option.first_name} ${option.last_name} (${option.email})`}
                        value={kebeleUsers.filter(u => groupForm.user_emails.includes(u.email))}
                        onChange={(event, newValues) => {
                          const emails = newValues.map(user => user.email);
                          setGroupForm({
                            ...groupForm,
                            user_emails: emails,
                            user_email: emails[0] || '' // Keep single email for backward compatibility
                          });
                        }}
                        renderInput={(params) => <TextField {...params} label="Select Users" fullWidth />}
                        renderOption={(props, option) => {
                          const { key, ...otherProps } = props;
                          return (
                            <Box component="li" key={key} {...otherProps}>
                              <Avatar sx={{ mr: 2, width: 32, height: 32 }}>
                                {option.first_name?.charAt(0) || option.username?.charAt(0)}
                              </Avatar>
                              <Box>
                                <Typography variant="body2">
                                  {`${option.first_name} ${option.last_name}`.trim() || option.username}
                                </Typography>
                                <Typography variant="caption" color="textSecondary">
                                  {option.email} • {option.role}
                                </Typography>
                              </Box>
                            </Box>
                          );
                        }}
                        renderTags={(value, getTagProps) =>
                          value.map((option, index) => (
                            <Chip
                              variant="outlined"
                              label={`${option.first_name} ${option.last_name}`.trim() || option.username}
                              {...getTagProps({ index })}
                              key={option.id}
                              avatar={
                                <Avatar sx={{ width: 24, height: 24 }}>
                                  {option.first_name?.charAt(0) || option.username?.charAt(0)}
                                </Avatar>
                              }
                            />
                          ))
                        }
                      />
                      <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
                        Select multiple users to add them all to {selectedGroupForManagement.name} at once.
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={groupForm.is_primary}
                            onChange={(e) => setGroupForm({ ...groupForm, is_primary: e.target.checked })}
                            color="primary"
                          />
                        }
                        label="Set as Primary Group"
                      />
                      <Typography variant="caption" color="textSecondary" sx={{ display: 'block' }}>
                        Primary group determines the user's main role and permissions.
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Reason (optional)"
                        multiline
                        rows={2}
                        value={groupForm.reason}
                        onChange={(e) => setGroupForm({ ...groupForm, reason: e.target.value })}
                        helperText="Reason for group assignment"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Box>
          ) : (
            <Box>
              {/* General User Assignment (when no specific group selected) */}
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12}>
                  <Autocomplete
                    options={kebeleUsers}
                    getOptionLabel={(option) => `${option.first_name} ${option.last_name} (${option.email})`}
                    value={kebeleUsers.find(u => u.email === groupForm.user_email) || null}
                    onChange={(event, newValue) =>
                      setGroupForm({ ...groupForm, user_email: newValue?.email || '' })
                    }
                    renderInput={(params) => <TextField {...params} label="Select User" fullWidth />}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Autocomplete
                    options={tenantGroups}
                    getOptionLabel={(option) => `${option.name} (${option.group_type})`}
                    value={tenantGroups.find(g => g.id === parseInt(groupForm.group_id)) || null}
                    onChange={(event, newValue) =>
                      setGroupForm({ ...groupForm, group_id: newValue?.id?.toString() || '' })
                    }
                    renderInput={(params) => <TextField {...params} label="Select Group" fullWidth />}
                    renderOption={(props, option) => {
                      const { key, ...otherProps } = props;
                      return (
                        <Box component="li" key={key} {...otherProps}>
                          <Box>
                            <Typography variant="body2">{option.name}</Typography>
                            <Typography variant="caption" color="textSecondary">
                              {option.group_type} • Level {option.level} • {option.users_count} users
                            </Typography>
                          </Box>
                        </Box>
                      );
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={groupForm.is_primary}
                        onChange={(e) => setGroupForm({ ...groupForm, is_primary: e.target.checked })}
                        color="primary"
                      />
                    }
                    label="Set as Primary Group"
                  />
                  <Typography variant="caption" color="textSecondary" sx={{ display: 'block' }}>
                    Primary group determines the user's main role and permissions.
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Reason (optional)"
                    multiline
                    rows={3}
                    value={groupForm.reason}
                    onChange={(e) => setGroupForm({ ...groupForm, reason: e.target.value })}
                    helperText="Reason for group assignment"
                  />
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignGroupDialogOpen(false)}>
            Close
          </Button>
          {(!selectedGroupForManagement || groupForm.user_email || groupForm.user_emails.length > 0) && (
            <Button
              onClick={handleAssignGroup}
              variant="contained"
              disabled={groupLoading || (!groupForm.user_email && groupForm.user_emails.length === 0) || !groupForm.group_id}
              startIcon={groupLoading ? <CircularProgress size={20} /> : <AssignmentIcon />}
            >
              {groupLoading ? 'Assigning...' : `Assign ${groupForm.user_emails.length > 1 ? `${groupForm.user_emails.length} Users` : 'User'} to Group`}
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Manage Group Permissions Dialog */}
      <Dialog
        open={grantPermissionDialogOpen}
        onClose={() => setGrantPermissionDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Manage Permissions: {selectedGroupForManagement?.name}
            </Typography>
            <IconButton onClick={() => setGrantPermissionDialogOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedGroupForManagement && (
            <Box>
              {/* Group Information */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                    <AssignmentIcon sx={{ mr: 1 }} />
                    Group Information
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Group Name</Typography>
                      <Typography variant="body1">{selectedGroupForManagement.name}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Type</Typography>
                      <Chip
                        label={selectedGroupForManagement.group_type}
                        size="small"
                        color={selectedGroupForManagement.group_type === 'administrative' ? 'success' : selectedGroupForManagement.group_type === 'functional' ? 'warning' : 'info'}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Level</Typography>
                      <Typography variant="body1">{selectedGroupForManagement.level}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Current Permissions</Typography>
                      <Typography variant="body1">{groupPermissions.length}</Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Permissions Management */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                    <VpnKeyIcon sx={{ mr: 1 }} />
                    Permissions ({permissions.length})
                  </Typography>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                    Check or uncheck permissions to add or remove them from this group.
                  </Typography>

                  {/* Permissions by Category */}
                  {Object.entries(
                    permissions.reduce((acc, perm) => {
                      const category = perm.content_type || 'General';
                      if (!acc[category]) acc[category] = [];
                      acc[category].push(perm);
                      return acc;
                    }, {})
                  ).map(([category, categoryPerms]) => (
                    <Accordion key={category} sx={{ mb: 1 }}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="subtitle1">
                          {category} ({categoryPerms.length})
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Grid container spacing={1}>
                          {categoryPerms.map((permission) => {
                            const isChecked = groupPermissions.some(gp => gp.codename === permission.codename);
                            return (
                              <Grid item xs={12} sm={6} md={4} key={permission.codename}>
                                <FormControlLabel
                                  control={
                                    <Checkbox
                                      checked={isChecked}
                                      onChange={(e) => handlePermissionToggle(permission, e.target.checked)}
                                      size="small"
                                    />
                                  }
                                  label={
                                    <Box>
                                      <Typography variant="body2">{permission.name}</Typography>
                                      <Typography variant="caption" color="textSecondary">
                                        {permission.codename}
                                      </Typography>
                                    </Box>
                                  }
                                />
                              </Grid>
                            );
                          })}
                        </Grid>
                      </AccordionDetails>
                    </Accordion>
                  ))}
                </CardContent>
              </Card>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setGrantPermissionDialogOpen(false)}>
            Close
          </Button>
          <Button
            onClick={handleSaveGroupPermissions}
            variant="contained"
            disabled={groupLoading}
            startIcon={groupLoading ? <CircularProgress size={20} /> : <VpnKeyIcon />}
          >
            {groupLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* User Details Dialog */}
      <Dialog
        open={userDetailsDialogOpen}
        onClose={() => setUserDetailsDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              {selectedUserDetails?.group ? 'Group Details' : 'User Details'}
            </Typography>
            <IconButton onClick={() => setUserDetailsDialogOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedUserDetails ? (
            <Box>
              {/* Group Information (if viewing group details) */}
              {selectedUserDetails.group && (
                <Card sx={{ mb: 3 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <AssignmentIcon sx={{ mr: 1 }} />
                      Group Information
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="textSecondary">Group Name</Typography>
                        <Typography variant="body1">{selectedUserDetails.group.name}</Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="textSecondary">Type</Typography>
                        <Chip
                          label={selectedUserDetails.group.group_type}
                          size="small"
                          color={selectedUserDetails.group.group_type === 'administrative' ? 'success' : selectedUserDetails.group.group_type === 'functional' ? 'warning' : 'info'}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="textSecondary">Level</Typography>
                        <Typography variant="body1">{selectedUserDetails.group.level}</Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="textSecondary">Users Count</Typography>
                        <Typography variant="body1">{selectedUserDetails.group.users_count || 0}</Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2" color="textSecondary">Description</Typography>
                        <Typography variant="body1">{selectedUserDetails.group.description || 'No description available'}</Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              )}

              {/* User Information (if viewing user details) */}
              {selectedUserDetails.user && (
                <Card sx={{ mb: 3 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <PersonIcon sx={{ mr: 1 }} />
                      User Information
                    </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Full Name</Typography>
                      <Typography variant="body1">
                        {selectedUserDetails.user.full_name || `${selectedUserDetails.user.first_name} ${selectedUserDetails.user.last_name}`}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Email</Typography>
                      <Typography variant="body1">{selectedUserDetails.user.email}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Username</Typography>
                      <Typography variant="body1">{selectedUserDetails.user.username}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Status</Typography>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Chip
                          label={selectedUserDetails.user.is_active ? 'Active' : 'Inactive'}
                          color={selectedUserDetails.user.is_active ? 'success' : 'error'}
                          size="small"
                        />
                        {selectedUserDetails.user.is_staff && (
                          <Chip label="Staff" color="info" size="small" />
                        )}
                        {selectedUserDetails.user.is_superuser && (
                          <Chip label="Superuser" color="warning" size="small" />
                        )}
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Date Joined</Typography>
                      <Typography variant="body1">
                        {new Date(selectedUserDetails.user.date_joined).toLocaleDateString()}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary">Last Login</Typography>
                      <Typography variant="body1">
                        {selectedUserDetails.user.last_login
                          ? new Date(selectedUserDetails.user.last_login).toLocaleDateString()
                          : 'Never'
                        }
                      </Typography>
                    </Grid>
                    {selectedUserDetails.user.tenant && (
                      <Grid item xs={12}>
                        <Typography variant="body2" color="textSecondary">Tenant</Typography>
                        <Typography variant="body1">
                          {selectedUserDetails.user.tenant.name} ({selectedUserDetails.user.tenant.type})
                          {selectedUserDetails.user.tenant.parent && (
                            <Typography variant="caption" color="textSecondary" sx={{ ml: 1 }}>
                              under {selectedUserDetails.user.tenant.parent}
                            </Typography>
                          )}
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                </CardContent>
              </Card>
              )}

              {/* Groups Information */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                    <AssignmentIcon sx={{ mr: 1 }} />
                    Groups ({selectedUserDetails.groups?.total_groups || 0})
                  </Typography>

                  {selectedUserDetails.groups?.primary_group && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="textSecondary">Primary Group</Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1">{selectedUserDetails.groups.primary_group.name}</Typography>
                        <Chip
                          label={`Level ${selectedUserDetails.groups.primary_group.level}`}
                          size="small"
                          color="primary"
                        />
                      </Box>
                    </Box>
                  )}

                  {selectedUserDetails.groups?.assigned_groups?.map((group, index) => (
                    <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                      <CardContent sx={{ pb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Box>
                            <Typography variant="subtitle1">{group.name}</Typography>
                            <Typography variant="caption" color="textSecondary">
                              ID: {group.id}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', gap: 0.5 }}>
                            <Chip
                              label={group.group_type}
                              size="small"
                              color={group.group_type === 'administrative' ? 'success' : group.group_type === 'functional' ? 'warning' : 'info'}
                            />
                            {group.is_primary && (
                              <Chip label="Primary" size="small" color="primary" />
                            )}
                          </Box>
                        </Box>

                        <Typography variant="body2" color="textSecondary" paragraph>
                          {group.description || `${group.group_type} group`}
                        </Typography>

                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="caption" color="textSecondary">
                            Level: {group.level}
                          </Typography>
                          {group.assigned_date && (
                            <Typography variant="caption" color="textSecondary">
                              Assigned: {new Date(group.assigned_date).toLocaleDateString()}
                            </Typography>
                          )}
                        </Box>

                        {group.permissions && group.permissions.length > 0 && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="caption" color="textSecondary">
                              Permissions ({group.permissions.length}):
                            </Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                              {group.permissions.slice(0, 5).map((perm) => (
                                <Chip
                                  key={perm.codename}
                                  label={perm.name}
                                  size="small"
                                  variant="outlined"
                                />
                              ))}
                              {group.permissions.length > 5 && (
                                <Chip
                                  label={`+${group.permissions.length - 5} more`}
                                  size="small"
                                  variant="outlined"
                                  color="primary"
                                />
                              )}
                            </Box>
                          </Box>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </CardContent>
              </Card>

              {/* Effective Permissions */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                    <VpnKeyIcon sx={{ mr: 1 }} />
                    Effective Permissions ({selectedUserDetails.permissions.total_permissions})
                  </Typography>

                  {Object.entries(selectedUserDetails.permissions.permissions_by_category || {}).map(([category, perms]) => (
                    <Accordion key={category} sx={{ mb: 1 }}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="subtitle2">
                          {category} ({perms.length})
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {perms.map((perm) => (
                            <Chip
                              key={perm.codename}
                              label={perm.name}
                              size="small"
                              variant="outlined"
                              title={perm.description}
                            />
                          ))}
                        </Box>
                      </AccordionDetails>
                    </Accordion>
                  ))}
                </CardContent>
              </Card>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUserDetailsDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Group Dialog */}
      <Dialog open={editGroupDialogOpen} onClose={() => setEditGroupDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Group: {editingGroup?.name}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Group Name *"
                value={groupForm.group_name}
                onChange={(e) => {
                  setGroupForm({ ...groupForm, group_name: e.target.value });
                  if (formErrors.group_name) {
                    setFormErrors({ ...formErrors, group_name: false });
                  }
                }}
                error={formErrors.group_name}
                helperText={formErrors.group_name ? "Group name is required" : "Display name for the group"}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={groupForm.description}
                onChange={(e) => setGroupForm({ ...groupForm, description: e.target.value })}
                helperText="Detailed description of the group's responsibilities"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Group Type</InputLabel>
                <Select
                  value={groupForm.group_type}
                  onChange={(e) => setGroupForm({ ...groupForm, group_type: e.target.value })}
                  label="Group Type"
                >
                  <MenuItem value="operational">Operational</MenuItem>
                  <MenuItem value="administrative">Administrative</MenuItem>
                  <MenuItem value="functional">Functional</MenuItem>
                  <MenuItem value="custom">Custom</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Hierarchy Level"
                type="number"
                value={groupForm.level}
                onChange={(e) => setGroupForm({ ...groupForm, level: parseInt(e.target.value) })}
                helperText="1-100 (higher = more authority)"
                inputProps={{ min: 1, max: 100 }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setEditGroupDialogOpen(false);
            resetGroupForm();
            setEditingGroup(null);
          }}>
            Cancel
          </Button>
          <Button
            onClick={handleEditGroup}
            variant="contained"
            disabled={groupLoading || !groupForm.group_name}
            startIcon={groupLoading ? <CircularProgress size={20} /> : <EditIcon />}
          >
            {groupLoading ? 'Updating...' : 'Update Group'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default KebeleUserManagement;
