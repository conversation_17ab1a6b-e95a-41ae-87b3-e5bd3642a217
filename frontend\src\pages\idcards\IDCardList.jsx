import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  Toolbar,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Avatar,
  Button,
  Card,
  CardContent,
  Grid,
  Tooltip,
  CircularProgress,
  Alert,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Visibility as ViewIcon,
  Print as PrintIcon,
  CheckCircle as ApprovedIcon,
  Cancel as RejectedIcon,
  Pending as PendingIcon,
  Badge as BadgeIcon,
  Assignment as DraftIcon,
  Verified as IssuedIcon,
  Refresh as RefreshIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Add as AddIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { usePermissions } from '../../hooks/usePermissions';
import { canApproveIDCards } from '../../utils/permissions';

/**
 * Get tenant ID from JWT token
 * @returns {number|null} The tenant ID or null if not found
 */
const getTenantId = () => {
  try {
    // First try to get from localStorage user object
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (user.tenant_id) {
      return user.tenant_id;
    }

    // Fallback: Get the access token from localStorage and decode it
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      console.warn('No access token found in localStorage');
      return null;
    }

    // Decode the JWT token to get tenant information
    const base64Url = accessToken.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    const tokenData = JSON.parse(jsonPayload);
    return tokenData.tenant_id || null;
  } catch (error) {
    console.error('Error getting tenant ID:', error);
    return null;
  }
};

const IDCardList = () => {
  const navigate = useNavigate();
  const { hasPermission } = usePermissions();
  const { user } = useAuth();

  // State management
  const [idCards, setIdCards] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [orderBy, setOrderBy] = useState('created_at');
  const [order, setOrder] = useState('desc');
  const [searchTerm, setSearchTerm] = useState('');
  const [totalCount, setTotalCount] = useState(0);

  // Statistics
  const [stats, setStats] = useState({
    total: 0,
    draft: 0,
    pending: 0,
    approved: 0,
    issued: 0,
    rejected: 0
  });

  useEffect(() => {
    fetchIDCards();
    fetchStats();
  }, [page, rowsPerPage, orderBy, order, searchTerm]);

  const fetchIDCards = async () => {
    try {
      setLoading(true);

      // Get tenant ID dynamically
      const tenantId = getTenantId();
      if (!tenantId) {
        setError('No tenant context found. Please log in again.');
        setLoading(false);
        return;
      }

      const params = {
        page: page + 1,
        page_size: rowsPerPage,
        ordering: order === 'desc' ? `-${orderBy}` : orderBy,
        search: searchTerm,
      };

      console.log('🔍 Fetching ID cards for tenant:', tenantId);
      const response = await axios.get(`/api/tenants/${tenantId}/idcards/`, {
        params
      });
      const idCardsData = response.data.results || [];
      setIdCards(idCardsData);
      setTotalCount(response.data.count || 0);
      console.log('✅ ID cards fetched:', idCardsData.length);

      // Debug: Log the first ID card structure
      if (idCardsData.length > 0) {
        console.log('🔍 First ID card structure:', idCardsData[0]);
        console.log('🔍 Available fields:', Object.keys(idCardsData[0]));
      }
    } catch (error) {
      console.error('Failed to fetch ID cards:', error);
      setError('Failed to load ID cards. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Get tenant ID dynamically
      const tenantId = getTenantId();
      if (!tenantId) {
        console.warn('No tenant context found for stats');
        return;
      }

      console.log('🔍 Fetching ID card stats for tenant:', tenantId);
      // For now, calculate stats from the current data
      // In the future, this could be a separate API endpoint
      const response = await axios.get(`/api/tenants/${tenantId}/idcards/`);

      const allCards = response.data.results || [];
      const statsData = {
        total: allCards.length,
        draft: allCards.filter(card => card.status === 'draft').length,
        pending: allCards.filter(card => card.status === 'pending_approval').length,
        approved: allCards.filter(card => card.status === 'approved').length,
        issued: allCards.filter(card => card.status === 'issued').length,
        rejected: allCards.filter(card => card.status === 'rejected').length,
      };

      setStats(statsData);
    } catch (error) {
      console.error('Failed to fetch ID card stats:', error);
      // Don't show error for stats, just log it
    }
  };

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleViewIDCard = (id) => {
    navigate(`/idcards/${id}`);
  };

  const handlePrintIDCard = (id) => {
    // Navigate to ID card detail page and auto-open print preview modal
    navigate(`/idcards/${id}?print=true`);
  };

  const handleSubmitForApproval = async (id) => {
    try {
      const tenantId = getTenantId();

      console.log('🔍 Submitting ID card for approval (from list):', {
        idCardId: id,
        tenantId: tenantId,
        userRole: user?.role,
        userEmail: user?.email,
        userIsSuperuser: user?.is_superuser
      });

      await axios.post(`/api/tenants/${tenantId}/idcards/${id}/approval_action/`, {
        action: 'submit_for_approval',
        comment: 'Submitted for kebele leader approval'
      });

      // Refresh the ID cards list
      fetchIDCards();
    } catch (error) {
      console.error('Error submitting for approval:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      // Show more specific error message
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Failed to submit for approval';
      setError(`Failed to submit for approval: ${errorMessage}`);
    }
  };

  const getStatusChip = (status) => {
    const statusConfig = {
      'draft': {
        icon: <DraftIcon />,
        label: 'Draft',
        color: 'default',
        bgcolor: 'grey.100',
        textColor: 'grey.700'
      },
      'pending_approval': {
        icon: <PendingIcon />,
        label: 'Pending Kebele Approval',
        color: 'warning',
        bgcolor: 'warning.light',
        textColor: 'warning.dark'
      },
      'kebele_approved': {
        icon: <ApprovedIcon />,
        label: 'Kebele Approved - Pending Subcity',
        color: 'info',
        bgcolor: 'info.light',
        textColor: 'info.dark'
      },
      'approved': {
        icon: <ApprovedIcon />,
        label: 'Fully Approved - Ready for Printing',
        color: 'success',
        bgcolor: 'success.light',
        textColor: 'success.dark'
      },
      'rejected': {
        icon: <RejectedIcon />,
        label: 'Rejected',
        color: 'error',
        bgcolor: 'error.light',
        textColor: 'error.dark'
      },
      'issued': {
        icon: <IssuedIcon />,
        label: 'Issued',
        color: 'primary',
        bgcolor: 'primary.light',
        textColor: 'primary.dark'
      },
      'printed': {
        icon: <PrintIcon />,
        label: 'Printed',
        color: 'info',
        bgcolor: 'info.light',
        textColor: 'info.dark'
      }
    };

    const config = statusConfig[status] || {
      icon: null,
      label: status || 'Unknown',
      color: 'default',
      bgcolor: 'grey.100',
      textColor: 'grey.700'
    };

    return (
      <Chip
        icon={config.icon}
        label={config.label}
        size="small"
        sx={{
          bgcolor: config.bgcolor,
          color: config.textColor,
          fontWeight: 'bold',
          '& .MuiChip-icon': {
            color: config.textColor
          }
        }}
      />
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const getCitizenName = (card) => {
    // Use the citizen_name field from the API response
    if (card?.citizen_name) {
      return card.citizen_name;
    }

    // Fallback to citizen object if available (for backward compatibility)
    const citizen = card?.citizen_details || card?.citizen;
    if (citizen) {
      return `${citizen.first_name || ''} ${citizen.middle_name || ''} ${citizen.last_name || ''}`.trim() || 'Unknown Citizen';
    }

    return 'Unknown Citizen';
  };

  const getCitizenDigitalId = (card) => {
    // Use the citizen_digital_id field from the API response
    if (card?.citizen_digital_id) {
      return card.citizen_digital_id;
    }

    // Fallback to citizen object if available (for backward compatibility)
    const citizen = card?.citizen_details || card?.citizen;
    if (citizen?.digital_id) {
      return citizen.digital_id;
    }

    return 'N/A';
  };

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" fontWeight="bold" sx={{ mb: 1 }}>
            ID Cards Management
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {totalCount} ID cards found
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          {hasPermission('manage_idcards') && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/idcards/create')}
              size="large"
              sx={{
                bgcolor: 'success.main',
                '&:hover': { bgcolor: 'success.dark' }
              }}
            >
              Create New ID Card
            </Button>
          )}
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <BadgeIcon />
                </Avatar>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="overline">
                    Total ID Cards
                  </Typography>
                  <Typography variant="h4">
                    {(stats.total || 0).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'grey.500', mr: 2 }}>
                  <DraftIcon />
                </Avatar>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="overline">
                    Draft
                  </Typography>
                  <Typography variant="h4">
                    {(stats.draft || 0).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <PendingIcon />
                </Avatar>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="overline">
                    Pending
                  </Typography>
                  <Typography variant="h4">
                    {(stats.pending || 0).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <ApprovedIcon />
                </Avatar>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="overline">
                    Approved
                  </Typography>
                  <Typography variant="h4">
                    {(stats.approved || 0).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <IssuedIcon />
                </Avatar>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="overline">
                    Issued
                  </Typography>
                  <Typography variant="h4">
                    {(stats.issued || 0).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'error.main', mr: 2 }}>
                  <RejectedIcon />
                </Avatar>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="overline">
                    Rejected
                  </Typography>
                  <Typography variant="h4">
                    {(stats.rejected || 0).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Filters */}
      <Paper sx={{ mb: 3, p: 2 }}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
          <TextField
            placeholder="Search by citizen name, digital ID..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300, flexGrow: 1 }}
            size="small"
          />
          <Button
            variant="contained"
            startIcon={<SearchIcon />}
            sx={{
              bgcolor: 'success.main',
              '&:hover': { bgcolor: 'success.dark' }
            }}
          >
            Search
          </Button>
          <Button
            variant="outlined"
            startIcon={<FilterIcon />}
            sx={{ borderColor: 'success.main', color: 'success.main' }}
          >
            Filter
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => {
              setSearchTerm('');
              fetchIDCards();
            }}
            sx={{ borderColor: 'success.main', color: 'success.main' }}
          >
            Refresh List
          </Button>
        </Box>
      </Paper>

      {/* ID Cards Table */}
      <Paper>
        <TableContainer>
          <Table stickyHeader>
            <TableHead>
              <TableRow sx={{ bgcolor: 'grey.50' }}>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  <TableSortLabel
                    active={orderBy === 'citizen__first_name'}
                    direction={orderBy === 'citizen__first_name' ? order : 'asc'}
                    onClick={() => handleRequestSort('citizen__first_name')}
                  >
                    CITIZEN NAME
                  </TableSortLabel>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  <TableSortLabel
                    active={orderBy === 'issue_date'}
                    direction={orderBy === 'issue_date' ? order : 'asc'}
                    onClick={() => handleRequestSort('issue_date')}
                  >
                    ISSUE DATE
                  </TableSortLabel>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  EXPIRY DATE
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  <TableSortLabel
                    active={orderBy === 'status'}
                    direction={orderBy === 'status' ? order : 'asc'}
                    onClick={() => handleRequestSort('status')}
                  >
                    STATUS
                  </TableSortLabel>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  SECURITY PATTERN
                </TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold', color: 'text.secondary', fontSize: '0.875rem' }}>
                  ACTIONS
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : idCards.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                    <Typography variant="body1" color="textSecondary">
                      No ID cards found
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                idCards.map((card) => (
                  <TableRow key={card.id} hover sx={{ '&:hover': { bgcolor: 'grey.50' } }}>
                    <TableCell sx={{ py: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          sx={{
                            width: 40,
                            height: 40,
                            bgcolor: 'grey.300',
                            color: 'white',
                            fontSize: '1rem',
                            fontWeight: 'bold',
                            mr: 2
                          }}
                        >
                          {getCitizenName(card)?.charAt(0)?.toUpperCase() || 'U'}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 0.5 }}>
                            {getCitizenName(card)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {getCitizenDigitalId(card)}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {formatDate(card.issue_date)}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Typography variant="body2">
                        {formatDate(card.expiry_date)}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      {getStatusChip(card.status)}
                    </TableCell>
                    <TableCell sx={{ py: 2 }}>
                      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                        {card.has_kebele_pattern && (
                          <Chip
                            label="🏛️ Kebele"
                            color="warning"
                            size="small"
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        )}
                        {card.has_subcity_pattern && (
                          <Chip
                            label="🏢 Subcity"
                            color="success"
                            size="small"
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        )}
                        {!card.has_kebele_pattern && !card.has_subcity_pattern && (
                          <Chip
                            label="⏳ None"
                            color="default"
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell align="center" sx={{ py: 2 }}>
                      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                        <IconButton
                          onClick={() => handleViewIDCard(card.id)}
                          size="small"
                          sx={{
                            bgcolor: 'primary.main',
                            color: 'white',
                            '&:hover': { bgcolor: 'primary.dark' }
                          }}
                        >
                          <ViewIcon fontSize="small" />
                        </IconButton>
                        {card.status === 'draft' && user?.role === 'clerk' && (
                          <Tooltip title="Send for Approval">
                            <IconButton
                              onClick={() => handleSubmitForApproval(card.id)}
                              size="small"
                              sx={{
                                bgcolor: 'warning.main',
                                color: 'white',
                                '&:hover': { bgcolor: 'warning.dark' }
                              }}
                            >
                              <PendingIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        {(card.status === 'approved' || card.status === 'issued') && hasPermission('print_idcards') && (
                          <Tooltip title="Print Preview">
                            <IconButton
                              onClick={() => handlePrintIDCard(card.id)}
                              size="small"
                              sx={{
                                bgcolor: 'info.main',
                                color: 'white',
                                '&:hover': { bgcolor: 'info.dark' }
                              }}
                            >
                              <PrintIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
          borderTop: '1px solid',
          borderColor: 'divider'
        }}>
          <Typography variant="body2" color="text.secondary">
            {totalCount} ID cards found
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Rows per page:
            </Typography>
            <Select
              value={rowsPerPage}
              onChange={handleChangeRowsPerPage}
              size="small"
              sx={{ minWidth: 80 }}
            >
              <MenuItem value={5}>5</MenuItem>
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </Select>
            <Typography variant="body2" color="text.secondary">
              {page * rowsPerPage + 1}–{Math.min((page + 1) * rowsPerPage, totalCount)} of {totalCount}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton
                onClick={(e) => handleChangePage(e, page - 1)}
                disabled={page === 0}
                size="small"
              >
                <ChevronLeftIcon />
              </IconButton>
              <IconButton
                onClick={(e) => handleChangePage(e, page + 1)}
                disabled={page >= Math.ceil(totalCount / rowsPerPage) - 1}
                size="small"
              >
                <ChevronRightIcon />
              </IconButton>
            </Box>
          </Box>
        </Box>
      </Paper>

    </Box>
  );
};

export default IDCardList;
