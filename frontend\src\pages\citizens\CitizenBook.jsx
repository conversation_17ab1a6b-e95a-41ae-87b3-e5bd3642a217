import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Chip,
  Avatar,
  Grid,
  Paper,
  Divider,
  CircularProgress,
  Alert,
  Pagination,
  InputAdornment,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  TableContainer
} from '@mui/material';
import {
  Search as SearchIcon,
  People as PeopleIcon,
  LocationCity as LocationCityIcon,
  LocationOn as LocationOnIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  Badge as BadgeIcon,
  CalendarToday as CalendarIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  FilterList as FilterIcon,
  Book as BookIcon,
  Business as BusinessIcon,
  Print as PrintIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import axios from '../../utils/axios';
import { useTheme as useAppTheme } from '../../contexts/ThemeContext';

const CitizenBook = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { tenantColors } = useAppTheme();
  const [citizenData, setCitizenData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    subcity: '',
    kebele: '',
    gender: '',
    age_group: ''
  });

  useEffect(() => {
    if (user) {
      fetchCitizenBook();
    }
  }, [user, page, filters]);

  const fetchCitizenBook = async () => {
    try {
      setLoading(true);
      
      // Get tenant ID
      let tenantId = user?.tenant_id;
      if (!tenantId) {
        const token = localStorage.getItem('accessToken');
        if (token) {
          const payload = JSON.parse(atob(token.split('.')[1]));
          tenantId = payload.tenant_id;
        }
      }

      if (!tenantId) {
        throw new Error('No tenant ID available');
      }

      const params = new URLSearchParams({
        page: page.toString(),
        page_size: '40',
        ...filters
      });

      const response = await axios.get(`/api/tenants/${tenantId}/citizens/citizen-book/data/?${params}`);
      setCitizenData(response.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching citizen book:', error);
      setError(`Failed to load citizen book: ${error.response?.data?.error || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPage(1); // Reset to first page when filtering
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleGenerateBook = () => {
    navigate('/citizens/citizen-book/print');
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ISSUED':
      case 'APPROVED':
      case 'PRINTED':
        return 'success';
      case 'PENDING':
      case 'KEBELE_APPROVED':
        return 'warning';
      case 'REJECTED':
        return 'error';
      default:
        return 'default';
    }
  };

  const getAgeGroupColor = (ageGroup) => {
    switch (ageGroup) {
      case '18-30':
        return '#10B981';
      case '31-50':
        return '#3B82F6';
      case '51+':
        return '#8B5CF6';
      default:
        return '#6B7280';
    }
  };

  if (!user || loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <CircularProgress size={60} />
          <Typography variant="h6" className="mt-4">
            Loading City Citizen Book...
          </Typography>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert severity="error" className="m-4">
        {error}
      </Alert>
    );
  }

  if (!citizenData) {
    return (
      <Alert severity="info" className="m-4">
        No citizen book data available
      </Alert>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <BookIcon className="text-4xl mr-3" style={{ color: tenantColors.primary }} />
            <div>
              <Typography variant="h4" className="font-bold" style={{ color: tenantColors.primary }}>
                {citizenData.city_info?.name} City Citizen Book
              </Typography>
              <Typography variant="subtitle1" className="text-gray-600">
                Comprehensive citizen directory across all sub-cities and kebeles
              </Typography>
            </div>
          </div>
          <Button
            variant="contained"
            startIcon={<PrintIcon />}
            onClick={handleGenerateBook}
            style={{ backgroundColor: tenantColors.primary, color: 'white' }}
            sx={{
              '&:hover': {
                backgroundColor: tenantColors.secondary,
              }
            }}
          >
            Generate Printable Book
          </Button>
        </div>

        {/* Statistics Cards */}
        <Grid container spacing={3} className="mb-6">
          <Grid item xs={12} sm={6} md={3}>
            <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <Typography variant="h4" className="font-bold">
                      {citizenData.statistics?.total_citizens?.toLocaleString() || 0}
                    </Typography>
                    <Typography variant="body2">Total Citizens</Typography>
                  </div>
                  <PeopleIcon className="text-4xl opacity-80" />
                </div>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <Typography variant="h4" className="font-bold">
                      {citizenData.statistics?.total_subcities || 0}
                    </Typography>
                    <Typography variant="body2">Sub-Cities</Typography>
                  </div>
                  <BusinessIcon className="text-4xl opacity-80" />
                </div>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <Typography variant="h4" className="font-bold">
                      {citizenData.statistics?.total_kebeles || 0}
                    </Typography>
                    <Typography variant="body2">Kebeles</Typography>
                  </div>
                  <LocationOnIcon className="text-4xl opacity-80" />
                </div>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card
              className="text-white"
              style={{
                background: `linear-gradient(to right, ${tenantColors.primary}, ${tenantColors.secondary})`
              }}
            >
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <Typography variant="h6" className="font-bold">
                      {citizenData.statistics?.total_male || 0}M / {citizenData.statistics?.total_female || 0}F
                    </Typography>
                    <Typography variant="body2">Gender Ratio</Typography>
                  </div>
                  <PeopleIcon className="text-4xl opacity-80" />
                </div>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent>
          <div className="flex items-center mb-4">
            <FilterIcon className="mr-2" style={{ color: tenantColors.primary }} />
            <Typography variant="h6" style={{ color: tenantColors.primary }}>
              Filters
            </Typography>
          </div>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Search Citizens"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                placeholder="Name, ID, Phone, Email..."
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Sub-City</InputLabel>
                <Select
                  value={filters.subcity}
                  onChange={(e) => handleFilterChange('subcity', e.target.value)}
                  label="Sub-City"
                >
                  <MenuItem value="">All Sub-Cities</MenuItem>
                  {citizenData.subcity_summary?.map((subcity) => (
                    <MenuItem key={subcity.id} value={subcity.id}>
                      {subcity.name} ({subcity.citizens_count})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Gender</InputLabel>
                <Select
                  value={filters.gender}
                  onChange={(e) => handleFilterChange('gender', e.target.value)}
                  label="Gender"
                >
                  <MenuItem value="">All Genders</MenuItem>
                  <MenuItem value="male">Male</MenuItem>
                  <MenuItem value="female">Female</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Age Group</InputLabel>
                <Select
                  value={filters.age_group}
                  onChange={(e) => handleFilterChange('age_group', e.target.value)}
                  label="Age Group"
                >
                  <MenuItem value="">All Ages</MenuItem>
                  <MenuItem value="18-30">18-30 years</MenuItem>
                  <MenuItem value="31-50">31-50 years</MenuItem>
                  <MenuItem value="51+">51+ years</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={3}>
              <Button
                variant="outlined"
                onClick={() => {
                  setFilters({
                    search: '',
                    subcity: '',
                    kebele: '',
                    gender: '',
                    age_group: ''
                  });
                  setPage(1);
                }}
                className="h-full"
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Citizens List */}
      <Card>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h6" style={{ color: tenantColors.primary }}>
              Citizens Directory ({citizenData.count?.toLocaleString() || 0} total)
            </Typography>
            <Typography variant="body2" className="text-gray-600">
              Page {page} of {citizenData.total_pages || 1}
            </Typography>
          </div>

          {citizenData.results?.length === 0 ? (
            <div className="text-center py-8">
              <PeopleIcon className="text-6xl text-gray-400 mb-4" />
              <Typography variant="h6" className="text-gray-500">
                No citizens found
              </Typography>
              <Typography variant="body2" className="text-gray-400">
                Try adjusting your filters or search criteria
              </Typography>
            </div>
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table size="small" sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Photo</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Name</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>ID Number</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Date of Birth</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Gender</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Phone</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}>Location</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {citizenData.results?.map((citizen, index) => (
                    <TableRow
                      key={`${citizen.kebele.id}-${citizen.id}`}
                      hover
                      sx={{
                        '&:nth-of-type(odd)': { backgroundColor: '#fafafa' },
                        height: '60px'
                      }}
                    >
                      {/* Photo */}
                      <TableCell sx={{ padding: '8px' }}>
                        <Avatar
                          sx={{
                            width: 40,
                            height: 40,
                            backgroundColor: citizen.gender === 'male' ? '#3B82F6' : '#EC4899',
                            color: 'white'
                          }}
                        >
                          {citizen.gender === 'male' ? <MaleIcon fontSize="small" /> : <FemaleIcon fontSize="small" />}
                        </Avatar>
                      </TableCell>

                      {/* Name */}
                      <TableCell sx={{ padding: '8px' }}>
                        <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.875rem' }}>
                          {citizen.full_name}
                        </Typography>
                        <Chip
                          size="small"
                          label={citizen.age_group}
                          sx={{
                            backgroundColor: getAgeGroupColor(citizen.age_group),
                            color: 'white',
                            fontSize: '0.7rem',
                            height: '20px',
                            mt: 0.5
                          }}
                        />
                      </TableCell>

                      {/* ID Number */}
                      <TableCell sx={{ padding: '8px' }}>
                        <Typography variant="body2" sx={{ fontSize: '0.875rem', fontFamily: 'monospace' }}>
                          {citizen.digital_id || 'N/A'}
                        </Typography>
                      </TableCell>

                      {/* Date of Birth */}
                      <TableCell sx={{ padding: '8px' }}>
                        <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                          {citizen.date_of_birth ? new Date(citizen.date_of_birth).toLocaleDateString() : 'N/A'}
                        </Typography>
                      </TableCell>

                      {/* Gender */}
                      <TableCell sx={{ padding: '8px' }}>
                        <Chip
                          icon={citizen.gender === 'male' ? <MaleIcon fontSize="small" /> : <FemaleIcon fontSize="small" />}
                          label={citizen.gender === 'male' ? 'Male' : 'Female'}
                          size="small"
                          color={citizen.gender === 'male' ? 'primary' : 'secondary'}
                          variant="outlined"
                          sx={{ fontSize: '0.75rem' }}
                        />
                      </TableCell>

                      {/* Phone */}
                      <TableCell sx={{ padding: '8px' }}>
                        <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                          {citizen.phone || 'N/A'}
                        </Typography>
                      </TableCell>

                      {/* Location */}
                      <TableCell sx={{ padding: '8px' }}>
                        <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 'medium' }}>
                          {citizen.kebele.name}
                        </Typography>
                        <Typography variant="caption" sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
                          {citizen.subcity.name}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* Pagination */}
          {citizenData.total_pages > 1 && (
            <div className="flex justify-center mt-6">
              <Pagination
                count={citizenData.total_pages}
                page={page}
                onChange={handlePageChange}
                color="primary"
                size="large"
                showFirstButton
                showLastButton
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sub-City Summary */}
      {citizenData.subcity_summary?.length > 0 && (
        <Card className="mt-6">
          <CardContent>
            <Typography variant="h6" className="mb-4" style={{ color: tenantColors.primary }}>
              Sub-City Summary
            </Typography>
            <Grid container spacing={3}>
              {citizenData.subcity_summary.map((subcity) => (
                <Grid item xs={12} md={6} lg={4} key={subcity.id}>
                  <Paper className="p-4 border-l-4" style={{ borderLeftColor: tenantColors.primary }}>
                    <Typography variant="h6" className="font-semibold mb-2">
                      {subcity.name}
                    </Typography>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Total Citizens:</span>
                        <span className="font-semibold">{subcity.citizens_count}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Male:</span>
                        <span>{subcity.male_count}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Female:</span>
                        <span>{subcity.female_count}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Kebeles:</span>
                        <span>{subcity.kebeles_count}</span>
                      </div>
                    </div>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CitizenBook;
