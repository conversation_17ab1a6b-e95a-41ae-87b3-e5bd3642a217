from django.db import models
from django.contrib.auth.models import Group, Permission
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class TenantGroup(models.Model):
    """
    Extended group model for tenant-specific groups with enhanced metadata.
    """
    group = models.OneToOneField(Group, on_delete=models.CASCADE, related_name='tenant_group')

    # Tenant scope
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, null=True, blank=True,
                              help_text="Tenant that owns this group (null for global groups)")

    # Group metadata
    description = models.TextField(blank=True, help_text="Detailed description of the group's purpose")
    is_system_group = models.BooleanField(default=False, help_text="True for system-defined groups")
    group_type = models.CharField(max_length=50, choices=[
        ('administrative', 'Administrative'),
        ('operational', 'Operational'),
        ('functional', 'Functional'),
        ('custom', 'Custom')
    ], default='custom')

    # Tenant type restrictions for global groups
    allowed_tenant_types = models.J<PERSON><PERSON>ield(
        default=list,
        blank=True,
        help_text="List of tenant types that can use this group (empty = all types allowed)"
    )

    # Hierarchy and management
    level = models.IntegerField(default=0, help_text="Group hierarchy level (higher = more authority)")
    parent_groups = models.ManyToManyField('self', blank=True, symmetrical=False,
                                          related_name='child_groups',
                                          help_text="Parent groups that this group inherits from")

    # Status and metadata
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='created_groups')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Tenant Group')
        verbose_name_plural = _('Tenant Groups')
        ordering = ['-level', 'group__name']
        unique_together = ['group', 'tenant']
    
    def __str__(self):
        scope = f" ({self.tenant.name})" if self.tenant else " (Global)"
        return f"{self.group.name}{scope}"
    
    @property
    def name(self):
        return self.group.name
    
    @property
    def users_count(self):
        return self.group.user_set.filter(is_active=True).count()

    def is_allowed_for_tenant_type(self, tenant_type):
        """Check if this group is allowed for the given tenant type"""
        if not self.allowed_tenant_types:
            return True  # No restrictions means allowed for all
        return tenant_type in self.allowed_tenant_types

    def is_allowed_for_tenant(self, tenant):
        """Check if this group is allowed for the given tenant"""
        if not tenant:
            return True  # Global context
        return self.is_allowed_for_tenant_type(tenant.type)
    
    @property
    def permissions_count(self):
        return self.group.permissions.count()
    
    def get_all_permissions(self):
        """Get all permissions including inherited from parent groups"""
        permissions = set(self.group.permissions.all())
        
        # Add permissions from parent groups
        for parent in self.parent_groups.all():
            permissions.update(parent.get_all_permissions())
        
        return list(permissions)
    
    def get_users(self):
        """Get all users in this group"""
        return self.group.user_set.filter(is_active=True)
    
    def add_user(self, user):
        """Add a user to this group"""
        self.group.user_set.add(user)
    
    def remove_user(self, user):
        """Remove a user from this group"""
        self.group.user_set.remove(user)
    
    def add_permission(self, permission):
        """Add a permission to this group"""
        self.group.permissions.add(permission)
    
    def remove_permission(self, permission):
        """Remove a permission from this group"""
        self.group.permissions.remove(permission)
    
    def has_permission(self, permission_codename):
        """Check if group has a specific permission"""
        return self.group.permissions.filter(codename=permission_codename).exists()


class GroupMembership(models.Model):
    """
    Track group membership with additional metadata.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    group = models.ForeignKey(TenantGroup, on_delete=models.CASCADE)
    
    # Membership metadata
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='assigned_memberships')
    assigned_at = models.DateTimeField(auto_now_add=True)
    reason = models.TextField(blank=True, help_text="Reason for group assignment")
    is_primary = models.BooleanField(default=False, help_text="Primary group for the user")
    
    # Status
    is_active = models.BooleanField(default=True)
    expires_at = models.DateTimeField(null=True, blank=True, help_text="Optional expiration date")
    
    class Meta:
        verbose_name = _('Group Membership')
        verbose_name_plural = _('Group Memberships')
        unique_together = ['user', 'group']
        ordering = ['-is_primary', '-assigned_at']
    
    def __str__(self):
        return f"{self.user.email} in {self.group.name}"
    
    @property
    def is_expired(self):
        if not self.expires_at:
            return False
        from django.utils import timezone
        return timezone.now() > self.expires_at


class GroupTemplate(models.Model):
    """
    Templates for creating groups with predefined permissions.
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    group_type = models.CharField(max_length=50, choices=[
        ('administrative', 'Administrative'),
        ('operational', 'Operational'),
        ('functional', 'Functional'),
        ('custom', 'Custom')
    ])
    
    # Template permissions
    permissions = models.ManyToManyField(Permission, blank=True)
    
    # Template metadata
    level = models.IntegerField(default=0)
    is_system_template = models.BooleanField(default=False)
    tenant_types = models.JSONField(default=list, blank=True,
                                   help_text="List of tenant types this template applies to")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Group Template')
        verbose_name_plural = _('Group Templates')
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def create_group(self, name, tenant=None, created_by=None):
        """Create a group from this template"""
        # Note: This method should be called within the appropriate schema context
        # The caller is responsible for setting the schema context

        # Create Django Group (in current schema context)
        django_group = Group.objects.create(name=name)

        # Create TenantGroup (in current schema context)
        tenant_group = TenantGroup.objects.create(
            group=django_group,
            tenant=tenant,
            description=self.description,
            group_type=self.group_type,
            level=self.level,
            created_by=created_by
        )

        # Add permissions (in current schema context)
        django_group.permissions.set(self.permissions.all())

        return tenant_group


# Extend User model with group-related methods
def get_user_groups(self):
    """Get all groups for this user"""
    return TenantGroup.objects.filter(group__user=self, is_active=True)

def get_primary_group(self):
    """Get the primary group for this user"""
    membership = GroupMembership.objects.filter(
        user=self, is_primary=True, is_active=True
    ).first()
    return membership.group if membership else None

def get_effective_permissions(self):
    """Get all effective permissions from all groups"""
    permissions = set()
    for group in self.get_user_groups():
        permissions.update(group.get_all_permissions())
    return list(permissions)

def has_group_permission(self, permission_codename):
    """Check if user has permission through any group"""
    for group in self.get_user_groups():
        if group.has_permission(permission_codename):
            return True
    return False

def add_to_group(self, group, assigned_by=None, reason='', is_primary=False):
    """Add user to a group"""
    membership, created = GroupMembership.objects.get_or_create(
        user=self,
        group=group,
        defaults={
            'assigned_by': assigned_by,
            'reason': reason,
            'is_primary': is_primary
        }
    )
    
    # Add to Django group
    group.group.user_set.add(self)
    
    return membership

def remove_from_group(self, group):
    """Remove user from a group"""
    GroupMembership.objects.filter(user=self, group=group).delete()
    group.group.user_set.remove(self)

# Add methods to User model
User.add_to_class('get_user_groups', get_user_groups)
User.add_to_class('get_primary_group', get_primary_group)
User.add_to_class('get_effective_permissions', get_effective_permissions)
User.add_to_class('has_group_permission', has_group_permission)
User.add_to_class('add_to_group', add_to_group)
User.add_to_class('remove_from_group', remove_from_group)
