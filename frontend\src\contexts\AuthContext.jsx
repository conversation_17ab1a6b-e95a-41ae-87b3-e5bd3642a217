import { createContext, useContext, useState, useCallback } from 'react'
import { jwtDecode } from 'jwt-decode'
import axios from '../utils/axios'

const AuthContext = createContext()

export const useAuth = () => useContext(AuthContext)

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const login = async (username, password) => {
    try {
      setLoading(true)
      setError(null)

      // Determine login type based on domain
      let loginData;
      if (username.includes('@')) {
        const domain = username.split('@')[1];
        // Check if it's a superadmin email (goid.com or similar system domains)
        if (domain === 'goid.com' || domain === 'localhost' || !domain.includes('.')) {
          // Superadmin login - use email field
          loginData = { email: username, password };
        } else {
          // Tenant user login - use username field for domain-based auth
          loginData = { username, password };
        }
      } else {
        // No @ symbol - treat as email for backward compatibility
        loginData = { email: username, password };
      }

      console.log('🔍 Login Debug:', { username, loginData });
      const response = await axios.post('/api/auth/token/', loginData)
      const { access, refresh, ...userData } = response.data

      localStorage.setItem('accessToken', access)
      localStorage.setItem('refreshToken', refresh)

      // Fetch user permissions
      const permissions = await fetchUserPermissions(userData.id)
      const userWithPermissions = { ...userData, permissions }

      setUser(userWithPermissions)
      return userWithPermissions
    } catch (err) {
      setError(err.response?.data?.detail || 'Login failed')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const fetchUserPermissions = async (userId) => {
    try {
      const accessToken = localStorage.getItem('accessToken')
      if (!accessToken || !userId) return []

      const response = await axios.get(`/api/users/${userId}/`, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })

      return response.data.permissions || []
    } catch (err) {
      console.error('Failed to fetch user permissions:', err)
      return []
    }
  }

  const logout = useCallback(() => {
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
    setUser(null)
  }, [])

  const checkAuth = useCallback(async () => {
    try {
      setLoading(true)
      const accessToken = localStorage.getItem('accessToken')
      const refreshToken = localStorage.getItem('refreshToken')

      if (!accessToken || !refreshToken) {
        setUser(null)
        return false
      }

      // Check if token is expired
      const decodedToken = jwtDecode(accessToken)
      const currentTime = Date.now() / 1000

      if (decodedToken.exp < currentTime) {
        // Token is expired, try to refresh
        try {
          const response = await axios.post('/api/auth/token/refresh/', { refresh: refreshToken })
          localStorage.setItem('accessToken', response.data.access)

          // Update user data
          const userData = jwtDecode(response.data.access)
          setUser({
            id: userData.user_id,
            email: userData.email,
            username: userData.username,
            role: userData.role,
            tenant_id: userData.tenant_id,
            tenant_name: userData.tenant_name,
            tenant_name_am: userData.tenant_name_am,  // Add Amharic name
            tenant_type: userData.tenant_type,
            tenant_schema: userData.tenant_schema,
            parent_tenant_id: userData.parent_tenant_id,
            parent_tenant_name: userData.parent_tenant_name,
            parent_tenant_name_am: userData.parent_tenant_name_am,  // Add Amharic name
            parent_tenant_type: userData.parent_tenant_type,
            city_tenant_id: userData.city_tenant_id,
            city_tenant_name: userData.city_tenant_name,
            city_tenant_name_am: userData.city_tenant_name_am,  // Add Amharic name
            city_tenant_type: userData.city_tenant_type,
            domain: userData.domain,
            is_superuser: userData.is_superuser,
          })

          return true
        } catch (refreshError) {
          // Refresh token is invalid
          logout()
          return false
        }
      } else {
        // Token is still valid
        const userData = decodedToken
        console.log('🔍 JWT Token Data:', userData);
        console.log('🔍 JWT Token Keys:', Object.keys(userData));
        console.log('🔍 JWT Token tenant_id:', userData.tenant_id);
        console.log('🔍 JWT Token tenant_name:', userData.tenant_name);

        const userObject = {
          id: userData.user_id,
          email: userData.email,
          username: userData.username,
          role: userData.role,
          tenant_id: userData.tenant_id,
          tenant_name: userData.tenant_name,
          tenant_name_am: userData.tenant_name_am,  // Add Amharic name
          tenant_type: userData.tenant_type,
          tenant_schema: userData.tenant_schema,
          parent_tenant_id: userData.parent_tenant_id,
          parent_tenant_name: userData.parent_tenant_name,
          parent_tenant_name_am: userData.parent_tenant_name_am,  // Add Amharic name
          parent_tenant_type: userData.parent_tenant_type,
          city_tenant_id: userData.city_tenant_id,
          city_tenant_name: userData.city_tenant_name,
          city_tenant_name_am: userData.city_tenant_name_am,  // Add Amharic name
          city_tenant_type: userData.city_tenant_type,
          domain: userData.domain,
          is_superuser: userData.is_superuser,
        };

        console.log('🔍 Setting user object:', userObject);
        console.log('🔍 User object tenant_id:', userObject.tenant_id);

        // Fetch user permissions
        const permissions = await fetchUserPermissions(userObject.id)
        const userWithPermissions = { ...userObject, permissions }

        setUser(userWithPermissions);
        return true
      }
    } catch (err) {
      logout()
      return false
    } finally {
      setLoading(false)
    }
  }, [logout])

  const getTenantId = useCallback(() => {
    console.log('🔍 getTenantId called - user:', user);
    console.log('🔍 getTenantId called - user?.tenant_id:', user?.tenant_id);
    const result = user?.tenant_id || null;
    console.log('🔍 getTenantId returning:', result);
    return result;
  }, [user]);

  const value = {
    user,
    loading,
    error,
    isAuthenticated: !!user,
    login,
    logout,
    checkAuth,
    getTenantId,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
