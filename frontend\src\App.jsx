import { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { CssBaseline } from '@mui/material'
import { useAuth } from './contexts/AuthContext'
import { useTheme } from './contexts/ThemeContext'
import { SharedDataProvider } from './contexts/SharedDataContext'

// Layouts
import MainLayout from './layouts/MainLayout'
import AuthLayout from './layouts/AuthLayout'
import LandingLayout from './layouts/LandingLayout'
import PublicLayout from './layouts/PublicLayout'

// Landing Page
import LandingPage from './pages/landing/LandingPage'

// Auth Pages
import Login from './pages/auth/Login'
import Register from './pages/auth/Register'
import ForgotPassword from './pages/auth/ForgotPassword'

// Dashboard Pages
import Dashboard from './pages/dashboard/Dashboard'

// Tenant Pages
import TenantList from './pages/tenants/TenantList'
import TenantCreate from './pages/tenants/TenantCreate'
import TenantEdit from './pages/tenants/TenantEdit'
import TenantDetails from './pages/tenants/TenantDetails'
import TenantEditForm from './pages/tenants/TenantEditForm'
import EnhancedTenantEditForm from './pages/tenants/EnhancedTenantEditForm'
import TenantRegistration from './pages/tenants/TenantRegistration'
import TenantThemeManager from './components/tenant/TenantThemeManager'

// System Management
import SystemSettingsManager from './components/system/SystemSettingsManager'

// User Pages
import UserList from './pages/users/UserList'
import UserCreate from './pages/users/UserCreate'
import UserEdit from './pages/users/UserEdit'
import UserProfile from './pages/users/UserProfile'
import KebeleUserManagement from './pages/users/KebeleUserManagement'

// Citizen Pages
import CitizensList from './pages/citizens/CitizensList'
import CitizenList from './pages/citizens/CitizenList'
import CitizenCreate from './pages/citizens/CitizenCreate'
import CitizenCreateStepper from './pages/citizens/CitizenCreateStepper'
import CitizenEdit from './pages/citizens/CitizenEdit'
import CitizenView from './pages/citizens/CitizenView'
import CitizenDetails from './pages/citizens/CitizenDetails'

// ID Card Pages
import IDCardList from './pages/idcards/IDCardList'
import IDCardCreate from './pages/idcards/IDCardCreate'
import IDCardCreateNew from './pages/idcards/IDCardCreateNew'
import IDCardView from './pages/idcards/IDCardView'
import IDCardPrint from './pages/idcards/IDCardPrint'
import IDCardPrintPreview from './pages/idcards/IDCardPrintPreview'
import PrintingQueue from './pages/idcards/PrintingQueue'
import SubcityPendingApprovals from './pages/idcards/SubcityPendingApprovals'
import SubcityIDCardsList from './pages/idcards/SubcityIDCardsList'
import SubcityCitizensList from './pages/citizens/SubcityCitizensList'
import CitizenBook from './pages/citizens/CitizenBook'
import CitizenBookPrintable from './pages/citizens/CitizenBookPrintable'
import IDCardDemo from './components/IDCardDemo'
import CitizenDirectory from './pages/citizens/CitizenDirectory'

// Not Found Page
import NotFound from './pages/NotFound'

// Transfer Pages
import TransfersList from './pages/transfers/TransfersList'
import TransferDetails from './pages/transfers/TransferDetails'

// Clearance Pages
import ClearancesList from './pages/clearances/ClearancesList'
import ClearanceDetails from './pages/clearances/ClearanceDetails'

// Test Pages
import DropdownTest from './pages/test/DropdownTest'

// ID Card Services
import IDCardServices from './pages/idcards/IDCardServices'

// Public Services
import PublicServicePortal from './components/public/PublicServicePortal'

// Route Protection
import RoleProtectedRoute from './components/common/RoleProtectedRoute'

// Hooks
// import { useTenantTheme } from './hooks/useTenantTheme' // Moved to ThemeContext

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth()

  if (loading) {
    return <div>Loading...</div>
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" />
  }

  return children
}

// Landing Route Component - Redirects to dashboard if authenticated
const LandingRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth()

  if (loading) {
    return <div>Loading...</div>
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" />
  }

  return children
}

function App() {
  const { theme } = useTheme()
  const { checkAuth } = useAuth()

  // Theme colors are now loaded in ThemeContext

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  return (
    <>
      <CssBaseline />
      <Routes>
        {/* Landing Page Route */}
        <Route element={
          <LandingRoute>
            <LandingLayout />
          </LandingRoute>
        }>
          <Route path="/" element={<LandingPage />} />
        </Route>

        {/* Auth Routes */}
        <Route element={<AuthLayout />}>
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
        </Route>

        {/* Public Test Routes */}
        <Route element={
          <SharedDataProvider>
            <AuthLayout />
          </SharedDataProvider>
        }>
          <Route path="/test/dropdown-public" element={<DropdownTest />} />
        </Route>

        {/* Public ID Card Services Routes */}
        <Route element={
          <SharedDataProvider>
            <PublicLayout />
          </SharedDataProvider>
        }>
          <Route path="/services" element={<PublicServicePortal />} />
          <Route path="/services/idcard" element={<IDCardServices />} />
          <Route path="/idcards/services" element={<IDCardServices />} />
        </Route>

        {/* Protected Routes */}
        <Route element={
          <ProtectedRoute>
            <SharedDataProvider>
              <MainLayout />
            </SharedDataProvider>
          </ProtectedRoute>
        }>
          <Route path="/dashboard" element={<Dashboard />} />

          {/* Tenant Routes - Only Superadmin */}
          <Route path="/tenants" element={
            <RoleProtectedRoute requiredPermission="view_tenants">
              <TenantList />
            </RoleProtectedRoute>
          } />
          <Route path="/tenants/create" element={
            <RoleProtectedRoute requiredPermission="create_tenants">
              <TenantCreate />
            </RoleProtectedRoute>
          } />
          <Route path="/tenants/register" element={
            <RoleProtectedRoute requiredPermission="create_tenants">
              <TenantRegistration />
            </RoleProtectedRoute>
          } />
          <Route path="/tenants/:id/details" element={
            <RoleProtectedRoute requiredPermission="view_tenants">
              <TenantDetails />
            </RoleProtectedRoute>
          } />
          <Route path="/tenants/:id/edit" element={
            <RoleProtectedRoute requiredPermission="edit_tenants">
              <EnhancedTenantEditForm />
            </RoleProtectedRoute>
          } />
          <Route path="/tenants/:id" element={
            <RoleProtectedRoute requiredPermission="edit_tenants">
              <TenantEdit />
            </RoleProtectedRoute>
          } />
          <Route path="/tenants/theme-manager" element={
            <RoleProtectedRoute requiredPermission="manage_tenants">
              <TenantThemeManager />
            </RoleProtectedRoute>
          } />
          <Route path="/system/settings" element={
            <RoleProtectedRoute requiredPermission="manage_tenants">
              <SystemSettingsManager />
            </RoleProtectedRoute>
          } />

          {/* User Routes - Only Admins */}
          <Route path="/users" element={
            <RoleProtectedRoute requiredPermission="manage_users">
              <UserList />
            </RoleProtectedRoute>
          } />
          <Route path="/users/create" element={
            <RoleProtectedRoute requiredPermission="manage_users">
              <UserCreate />
            </RoleProtectedRoute>
          } />
          <Route path="/users/kebeles" element={
            <RoleProtectedRoute requiredPermission="manage_users">
              <KebeleUserManagement />
            </RoleProtectedRoute>
          } />
          <Route path="/users/:id" element={
            <RoleProtectedRoute requiredPermission="manage_users">
              <UserEdit />
            </RoleProtectedRoute>
          } />
          <Route path="/profile" element={<UserProfile />} />

          {/* Citizen Routes */}
          <Route path="/citizens" element={<CitizensList />} />
          <Route path="/citizens/all-kebeles" element={
            <RoleProtectedRoute requiredPermission="manage_citizens">
              <SubcityCitizensList />
            </RoleProtectedRoute>
          } />
          <Route path="/citizens/citizen-book" element={
            <RoleProtectedRoute requiredPermission="manage_citizens">
              <CitizenDirectory />
            </RoleProtectedRoute>
          } />
          <Route path="/citizens/citizen-book/print" element={
            <RoleProtectedRoute requiredPermission="manage_citizens">
              <CitizenBookPrintable />
            </RoleProtectedRoute>
          } />
          <Route path="/citizens/create" element={
            <RoleProtectedRoute requiredPermission="register_citizens">
              <CitizenCreateStepper />
            </RoleProtectedRoute>
          } />
          <Route path="/citizens/create-simple" element={
            <RoleProtectedRoute requiredPermission="register_citizens">
              <CitizenCreate />
            </RoleProtectedRoute>
          } />
          <Route path="/citizens/:id" element={<CitizenDetails />} />
          <Route path="/citizens/:id/edit" element={<CitizenEdit />} />
          <Route path="/citizens/:id/view" element={<CitizenView />} />
          <Route path="/tenants/:tenantId/citizens/:id" element={<CitizenDetails />} />

          {/* Transfer Routes - Only for Kebele Leaders and Admins */}
          <Route path="/transfers" element={<TransfersList />} />
          <Route path="/transfers/:id" element={<TransferDetails />} />

          {/* Clearance Routes - Simplified workflow but with tracking pages */}
          <Route path="/clearances" element={<ClearancesList />} />
          <Route path="/clearances/:id" element={<ClearanceDetails />} />

          {/* ID Card Routes */}
          <Route path="/idcards" element={<IDCardList />} />
          <Route path="/idcards/all-kebeles" element={
            <RoleProtectedRoute requiredPermission="manage_idcards">
              <SubcityIDCardsList />
            </RoleProtectedRoute>
          } />
          <Route path="/idcards/pending-subcity-approval" element={
            <RoleProtectedRoute requiredPermission="approve_idcards">
              <SubcityPendingApprovals />
            </RoleProtectedRoute>
          } />
          <Route path="/idcards/printing-queue" element={
            <RoleProtectedRoute requiredPermission="print_idcards">
              <PrintingQueue />
            </RoleProtectedRoute>
          } />
          <Route path="/citizens/:citizenId/idcards/create" element={
            <RoleProtectedRoute requiredPermission="create_idcards">
              <IDCardCreateNew />
            </RoleProtectedRoute>
          } />
          <Route path="/idcards/create" element={
            <RoleProtectedRoute requiredPermission="create_idcards">
              <IDCardCreateNew />
            </RoleProtectedRoute>
          } />
          <Route path="/idcards/:id" element={<IDCardView />} />
          <Route path="/idcards/:id/print" element={<IDCardPrint />} />
          <Route path="/idcards/:id/print-preview" element={
            <RoleProtectedRoute requiredPermission="print_idcards">
              <IDCardPrintPreview />
            </RoleProtectedRoute>
          } />
          <Route path="/tenants/:tenantId/idcards" element={<IDCardList />} />
          <Route path="/tenants/:tenantId/idcards/:id" element={<IDCardView />} />
          <Route path="/tenants/:tenantId/idcards/:id/print" element={<IDCardPrint />} />
          <Route path="/tenants/:tenantId/idcards/:id/print-preview" element={
            <RoleProtectedRoute requiredPermission="print_idcards">
              <IDCardPrintPreview />
            </RoleProtectedRoute>
          } />

          {/* Test Routes */}
          <Route path="/test/dropdown" element={<DropdownTest />} />
          <Route path="/demo/idcard" element={<IDCardDemo />} />
        </Route>

        {/* Not Found Route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </>
  )
}

export default App
