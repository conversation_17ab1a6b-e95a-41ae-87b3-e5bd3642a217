from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.core.management import call_command
from tenants.models import Tenant, Domain, TenantType
from django_tenants.utils import schema_context
from datetime import datetime, timedelta

User = get_user_model()

class Command(BaseCommand):
    help = 'Initialize the database with sample data'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Initializing data...'))

        try:
            self.create_superuser()
            self.create_public_tenant()
            self.create_sample_tenants()
            self.stdout.write(self.style.SUCCESS('Data initialization complete.'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error during data initialization: {e}'))
            # Don't raise the exception - let the server start anyway
            self.stdout.write(self.style.WARNING('Continuing with server startup despite initialization errors...'))

    def create_superuser(self):
        """Create a superuser if it doesn't exist."""
        if not User.objects.filter(username='admin').exists():
            User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                role='superadmin'
            )
            self.stdout.write(self.style.SUCCESS('Superuser created successfully.'))
        else:
            self.stdout.write(self.style.WARNING('Superuser already exists.'))

    def create_public_tenant(self):
        """Create the public tenant if it doesn't exist."""
        if not Tenant.objects.filter(schema_name='public').exists():
            tenant = Tenant(
                schema_name='public',
                name='Public',
                type=TenantType.CITY
            )
            tenant.save()

            domain = Domain()
            domain.domain = 'localhost'
            domain.tenant = tenant
            domain.is_primary = True
            domain.save()

            self.stdout.write(self.style.SUCCESS('Public tenant created successfully.'))
        else:
            self.stdout.write(self.style.WARNING('Public tenant already exists.'))

    def create_sample_tenants(self):
        """Create sample tenants for testing."""
        # Create a city using get_or_create to avoid deletion issues
        city, city_created = Tenant.objects.get_or_create(
            schema_name='city_addis_ababa',
            defaults={
                'name': 'Addis Ababa',
                'type': TenantType.CITY
            }
        )

        if city_created:
            self.stdout.write(self.style.SUCCESS('City tenant created successfully.'))
        else:
            self.stdout.write(self.style.WARNING('City tenant already exists.'))

        # Create domain for city
        domain, domain_created = Domain.objects.get_or_create(
            domain='addis.goid.local',
            defaults={
                'tenant': city,
                'is_primary': True
            }
        )
        if domain_created:
            self.stdout.write(self.style.SUCCESS('City domain created successfully.'))
        else:
            self.stdout.write(self.style.WARNING('City domain already exists.'))

        # Run migrations for the city tenant (whether created or existing)
        try:
            with schema_context(city.schema_name):
                call_command('migrate', verbosity=1)
        except Exception as e:
            self.stdout.write(self.style.WARNING(f'Migration error for city tenant: {e}'))

        # Create a subcity using get_or_create to avoid deletion issues
        subcity, subcity_created = Tenant.objects.get_or_create(
            schema_name='subcity_bole',
            defaults={
                'name': 'Bole',
                'type': TenantType.SUBCITY,
                'parent': city
            }
        )

        if subcity_created:
            self.stdout.write(self.style.SUCCESS('Subcity tenant created successfully.'))
        else:
            self.stdout.write(self.style.WARNING('Subcity tenant already exists.'))

        # Create domain for subcity
        domain, domain_created = Domain.objects.get_or_create(
            domain='bole.goid.local',
            defaults={
                'tenant': subcity,
                'is_primary': True
            }
        )
        if domain_created:
            self.stdout.write(self.style.SUCCESS('Subcity domain created successfully.'))
        else:
            self.stdout.write(self.style.WARNING('Subcity domain already exists.'))

        # Run migrations for the subcity tenant (whether created or existing)
        try:
            with schema_context(subcity.schema_name):
                call_command('migrate', verbosity=1)
        except Exception as e:
            self.stdout.write(self.style.WARNING(f'Migration error for subcity tenant: {e}'))

        # Create a kebele using get_or_create to avoid deletion issues
        kebele, kebele_created = Tenant.objects.get_or_create(
            schema_name='kebele_bole_01',
            defaults={
                'name': 'Bole 01',
                'type': TenantType.KEBELE,
                'parent': subcity
            }
        )

        if kebele_created:
            self.stdout.write(self.style.SUCCESS('Kebele tenant created successfully.'))
        else:
            self.stdout.write(self.style.WARNING('Kebele tenant already exists.'))

        # Create domain for kebele
        domain, domain_created = Domain.objects.get_or_create(
            domain='bole01.goid.local',
            defaults={
                'tenant': kebele,
                'is_primary': True
            }
        )
        if domain_created:
            self.stdout.write(self.style.SUCCESS('Kebele domain created successfully.'))
        else:
            self.stdout.write(self.style.WARNING('Kebele domain already exists.'))

        # Run migrations for the kebele tenant (whether created or existing)
        try:
            with schema_context(kebele.schema_name):
                call_command('migrate', verbosity=1)
        except Exception as e:
            self.stdout.write(self.style.WARNING(f'Migration error for kebele tenant: {e}'))

        # Create users for each tenant
        with schema_context('city_addis_ababa'):
            if not User.objects.filter(username='city_admin').exists():
                User.objects.create_user(
                    username='city_admin',
                    email='<EMAIL>',
                    password='password123',
                    role='city_admin',
                    tenant=city
                )
                self.stdout.write(self.style.SUCCESS('City admin created successfully.'))

        with schema_context('subcity_bole'):
            if not User.objects.filter(username='subcity_admin').exists():
                User.objects.create_user(
                    username='subcity_admin',
                    email='<EMAIL>',
                    password='password123',
                    role='subcity_admin',
                    tenant=subcity
                )
                self.stdout.write(self.style.SUCCESS('Subcity admin created successfully.'))

        with schema_context('kebele_bole_01'):
            if not User.objects.filter(username='kebele_admin').exists():
                User.objects.create_user(
                    username='kebele_admin',
                    email='<EMAIL>',
                    password='password123',
                    role='kebele_admin',
                    tenant=kebele
                )
                self.stdout.write(self.style.SUCCESS('Kebele admin created successfully.'))

            if not User.objects.filter(username='clerk').exists():
                User.objects.create_user(
                    username='clerk',
                    email='<EMAIL>',
                    password='password123',
                    role='clerk',
                    tenant=kebele
                )
                self.stdout.write(self.style.SUCCESS('Clerk created successfully.'))

        # Create sample citizens and ID cards in the kebele tenant
        self.create_sample_citizens(kebele)

    def create_sample_citizens(self, kebele):
        """Create sample citizens and ID cards for testing."""
        from citizens.models import Citizen, Gender, MaritalStatus, BloodType
        from idcards.models import IDCard, IDCardStatus
        from workflows.models import WorkflowLog, ApprovalAction

        with schema_context(kebele.schema_name):
            # Get the clerk user
            clerk = User.objects.filter(role='clerk').first()

            if not clerk:
                self.stdout.write(self.style.WARNING('No clerk found in the kebele tenant.'))
                return

            # Create citizens if they don't exist
            if not Citizen.objects.exists():
                # Create 5 sample citizens
                citizens = [
                    {
                        'first_name': 'Abebe',
                        'middle_name': 'Kebede',
                        'last_name': 'Tadesse',
                        'first_name_am': 'አበበ',  # Added: Amharic first name
                        'middle_name_am': 'ከበደ',  # Added: Amharic middle name
                        'last_name_am': 'ታደሰ',  # Added: Amharic last name
                        'date_of_birth': '1985-05-15',
                        'gender': Gender.MALE,
                        'phone': '+251911234567',  # Fixed: phone_number -> phone
                        'email': '<EMAIL>',
                        'nationality': 1,  # Fixed: Use integer ID instead of string
                        'marital_status': 2,  # Fixed: Use integer ID instead of enum
                        'employment': 'employed',  # Fixed: occupation -> employment
                        'employee_type': 'permanent',  # Added: employee_type field
                        'organization_name': 'Addis Ababa University',  # Added: organization_name
                    },
                    {
                        'first_name': 'Sara',
                        'middle_name': 'Mohammed',
                        'last_name': 'Ali',
                        'first_name_am': 'ሳራ',  # Added: Amharic first name
                        'middle_name_am': 'መሐመድ',  # Added: Amharic middle name
                        'last_name_am': 'አሊ',  # Added: Amharic last name
                        'date_of_birth': '1990-08-22',
                        'gender': Gender.FEMALE,
                        'phone': '+251922345678',  # Fixed: phone_number -> phone
                        'email': '<EMAIL>',
                        'nationality': 1,  # Fixed: Use integer ID instead of string
                        'marital_status': 1,  # Fixed: Use integer ID instead of enum
                        'employment': 'employed',  # Fixed: occupation -> employment
                        'employee_type': 'permanent',  # Added: employee_type field
                        'organization_name': 'Black Lion Hospital',  # Added: organization_name
                    },
                    {
                        'first_name': 'Daniel',
                        'middle_name': 'Tesfaye',
                        'last_name': 'Bekele',
                        'first_name_am': 'ዳንኤል',  # Added: Amharic first name
                        'middle_name_am': 'ተስፋዬ',  # Added: Amharic middle name
                        'last_name_am': 'በቀለ',  # Added: Amharic last name
                        'date_of_birth': '1978-12-10',
                        'gender': Gender.MALE,
                        'phone': '+251933456789',  # Fixed: phone_number -> phone
                        'email': '<EMAIL>',
                        'nationality': 1,  # Fixed: Use integer ID instead of string
                        'marital_status': 3,  # Fixed: Use integer ID instead of enum
                        'employment': 'employed',  # Fixed: occupation -> employment
                        'employee_type': 'contract',  # Added: employee_type field
                        'organization_name': 'Ethiopian Electric Power',  # Added: organization_name
                    },
                ]

                created_citizens = []
                for citizen_data in citizens:
                    date_of_birth = datetime.strptime(citizen_data.pop('date_of_birth'), '%Y-%m-%d').date()
                    citizen = Citizen.objects.create(
                        **citizen_data,
                        date_of_birth=date_of_birth,
                        # Removed created_by field as it doesn't exist in the Citizen model
                    )
                    created_citizens.append(citizen)
                    self.stdout.write(self.style.SUCCESS(f"Created citizen: {citizen.first_name} {citizen.middle_name}"))

                # Create ID cards for some citizens
                # Get the kebele admin
                kebele_admin = User.objects.filter(role='kebele_admin').first()

                if kebele_admin:
                    # Create ID cards with different statuses
                    id_cards = [
                        {
                            'citizen': created_citizens[0],
                            'status': IDCardStatus.APPROVED,
                            'issue_date': datetime.now().date(),
                            'expiry_date': datetime.now().date() + timedelta(days=5*365),
                            'created_by': clerk,
                            'approved_by': kebele_admin,
                        },
                        {
                            'citizen': created_citizens[1],
                            'status': IDCardStatus.PENDING_APPROVAL,
                            'created_by': clerk,
                        },
                        {
                            'citizen': created_citizens[2],
                            'status': IDCardStatus.DRAFT,
                            'created_by': clerk,
                        },
                    ]

                    for id_card_data in id_cards:
                        id_card = IDCard.objects.create(**id_card_data)
                        self.stdout.write(self.style.SUCCESS(f"Created ID card: {id_card.card_number} for {id_card.citizen.first_name}"))

                        # Create workflow logs for the ID card
                        if id_card.status == IDCardStatus.APPROVED:
                            # Create submit log
                            WorkflowLog.objects.create(
                                id_card=id_card,
                                action=ApprovalAction.SUBMIT,
                                from_status=IDCardStatus.DRAFT,
                                to_status=IDCardStatus.PENDING_APPROVAL,
                                comment='Submitted for approval',
                                performed_by=clerk,
                                performed_at=datetime.now() - timedelta(days=2)
                            )

                            # Create approve log
                            WorkflowLog.objects.create(
                                id_card=id_card,
                                action=ApprovalAction.APPROVE,
                                from_status=IDCardStatus.PENDING_APPROVAL,
                                to_status=IDCardStatus.APPROVED,
                                comment='Approved',
                                performed_by=kebele_admin,
                                performed_at=datetime.now() - timedelta(days=1)
                            )
                        elif id_card.status == IDCardStatus.PENDING_APPROVAL:
                            # Create submit log
                            WorkflowLog.objects.create(
                                id_card=id_card,
                                action=ApprovalAction.SUBMIT,
                                from_status=IDCardStatus.DRAFT,
                                to_status=IDCardStatus.PENDING_APPROVAL,
                                comment='Submitted for approval',
                                performed_by=clerk,
                                performed_at=datetime.now() - timedelta(days=1)
                            )

                    self.stdout.write(self.style.SUCCESS(f"Created {len(created_citizens)} citizens and {len(id_cards)} ID cards."))
                else:
                    self.stdout.write(self.style.WARNING("No kebele admin found. ID cards not created."))
            else:
                self.stdout.write(self.style.WARNING("Citizens already exist in the kebele tenant."))
