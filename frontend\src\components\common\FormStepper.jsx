import { useState } from 'react';
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  Button,
  Typography,
  Paper,
  useTheme,
  alpha,
} from '@mui/material';
import {
  NavigateNext as NextIcon,
  NavigateBefore as BackIcon,
  Check as CheckIcon,
} from '@mui/icons-material';

const FormStepper = ({
  steps,
  activeStep,
  onNext,
  onBack,
  onSubmit,
  isLastStep,
  isFirstStep,
  canProceed = true,
  loading = false,
  children,
  sx = {},
}) => {
  const theme = useTheme();

  return (
    <Box sx={{ width: '100%', ...sx }}>
      {/* Stepper Header */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          background: theme.palette.mode === 'light'
            ? alpha(theme.palette.primary.main, 0.03)
            : alpha(theme.palette.primary.main, 0.05),
          border: `1px solid ${theme.palette.mode === 'light'
            ? alpha(theme.palette.primary.main, 0.1)
            : alpha(theme.palette.primary.main, 0.2)}`,
          borderRadius: 2,
        }}
      >
        <Stepper 
          activeStep={activeStep} 
          alternativeLabel
          sx={{
            '& .MuiStepLabel-root .Mui-completed': {
              color: theme.palette.success.main,
            },
            '& .MuiStepLabel-root .Mui-active': {
              color: theme.palette.primary.main,
            },
            '& .MuiStepConnector-alternativeLabel': {
              top: 10,
              left: 'calc(-50% + 16px)',
              right: 'calc(50% + 16px)',
            },
            '& .MuiStepConnector-alternativeLabel.Mui-active .MuiStepConnector-line': {
              borderColor: theme.palette.primary.main,
            },
            '& .MuiStepConnector-alternativeLabel.Mui-completed .MuiStepConnector-line': {
              borderColor: theme.palette.success.main,
            },
          }}
        >
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel
                StepIconComponent={({ active, completed }) => (
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '0.875rem',
                      fontWeight: 'bold',
                      backgroundColor: completed
                        ? theme.palette.success.main
                        : active
                        ? theme.palette.primary.main
                        : theme.palette.grey[300],
                      color: completed || active ? 'white' : theme.palette.grey[600],
                      transition: 'all 0.3s ease',
                    }}
                  >
                    {completed ? <CheckIcon fontSize="small" /> : index + 1}
                  </Box>
                )}
              >
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: activeStep === index ? 600 : 400,
                    color: activeStep === index
                      ? theme.palette.primary.main
                      : theme.palette.text.secondary,
                  }}
                >
                  {step.label}
                </Typography>
                {step.description && (
                  <Typography
                    variant="caption"
                    sx={{
                      display: 'block',
                      color: theme.palette.text.secondary,
                      mt: 0.5,
                    }}
                  >
                    {step.description}
                  </Typography>
                )}
              </StepLabel>
            </Step>
          ))}
        </Stepper>
      </Paper>

      {/* Step Content */}
      <Box sx={{ mb: 3 }}>
        {children}
      </Box>

      {/* Navigation Buttons */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          background: theme.palette.mode === 'light'
            ? alpha(theme.palette.grey[100], 0.5)
            : alpha(theme.palette.grey[800], 0.5),
          borderRadius: 2,
        }}
      >
        <Button
          onClick={onBack}
          disabled={isFirstStep || loading}
          startIcon={<BackIcon />}
          variant="outlined"
          sx={{
            visibility: isFirstStep ? 'hidden' : 'visible',
          }}
        >
          Back
        </Button>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Step {activeStep + 1} of {steps.length}
          </Typography>
          
          {isLastStep ? (
            <Button
              onClick={onSubmit}
              disabled={!canProceed || loading}
              variant="contained"
              size="large"
              sx={{
                minWidth: 120,
                background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                '&:hover': {
                  background: `linear-gradient(135deg, ${theme.palette.success.light} 0%, ${theme.palette.success.main} 100%)`,
                },
              }}
            >
              {loading ? 'Submitting...' : 'Submit'}
            </Button>
          ) : (
            <Button
              onClick={onNext}
              disabled={!canProceed || loading}
              endIcon={<NextIcon />}
              variant="contained"
              size="large"
              sx={{ minWidth: 120 }}
            >
              Next
            </Button>
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default FormStepper;
