#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from django.contrib.auth import get_user_model
from tenants.models import Tenant
from django_tenants.utils import schema_context
from users.serializers import UserSerializer

User = get_user_model()

print('=== Testing UserSerializer with groups ===')

# First, let's see which users actually have group memberships
from users.models_groups import GroupMembership
print('Users with group memberships:')
for membership in GroupMembership.objects.all():
    print(f'  {membership.user.email} -> {membership.group.name}')

print('\n=== Testing specific users with groups ===')
# Test with users that actually have groups
test_emails = ['<EMAIL>', '<EMAIL>']

for email in test_emails:
    try:
        # Find user across all schemas
        user = None
        user_tenant = None

        # Try public schema first
        try:
            user = User.objects.get(email=email)
            user_tenant = None
            print(f'\nFound {email} in public schema')
        except User.DoesNotExist:
            # Search tenant schemas
            for tenant in Tenant.objects.all():
                with schema_context(tenant.schema_name):
                    try:
                        user = User.objects.get(email=email)
                        user_tenant = tenant
                        print(f'\nFound {email} in tenant schema: {tenant.name}')
                        break
                    except User.DoesNotExist:
                        continue

        if user:
            # Test serializer
            if user_tenant:
                with schema_context(user_tenant.schema_name):
                    serializer = UserSerializer(user)
                    data = serializer.data
            else:
                serializer = UserSerializer(user)
                data = serializer.data

            print(f'  Primary Group ID: {data.get("primary_group_id")}')
            print(f'  Primary Group Name: {data.get("primary_group_name")}')
            groups = data.get('groups', [])
            print(f'  All Groups Count: {len(groups)}')
            for group in groups:
                print(f'    - {group["name"]} (ID: {group["id"]}, Primary: {group["is_primary"]})')
        else:
            print(f'\nUser {email} not found')

    except Exception as e:
        print(f'\nError testing {email}: {e}')
