#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append('/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'goid.settings')
django.setup()

from tenants.models import Tenant
from django_tenants.utils import schema_context
from users.serializers import UserSerializer
from django.contrib.auth import get_user_model

User = get_user_model()

print('=== Testing API Response for Kebele15 Users ===')
tenant = Tenant.objects.get(name='Kebele15')
print(f'Tenant: {tenant.name} (ID: {tenant.id})')

with schema_context(tenant.schema_name):
    users = User.objects.filter(tenant=tenant)
    print(f'Found {users.count()} users in {tenant.name}')
    
    for user in users:
        serializer = UserSerializer(user)
        data = serializer.data
        print(f'\nUser: {data["email"]}')
        print(f'  Primary Group ID: {data.get("primary_group_id")}')
        print(f'  Primary Group Name: {data.get("primary_group_name")}')
        groups = data.get('groups', [])
        print(f'  Groups ({len(groups)}):')
        for group in groups:
            print(f'    - {group["name"]} (ID: {group["id"]}, Primary: {group["is_primary"]})')

print('\n=== Testing API Response for Kebele14 Users ===')
tenant = Tenant.objects.get(name='Kebele14')
print(f'Tenant: {tenant.name} (ID: {tenant.id})')

with schema_context(tenant.schema_name):
    users = User.objects.filter(tenant=tenant)
    print(f'Found {users.count()} users in {tenant.name}')
    
    for user in users:
        serializer = UserSerializer(user)
        data = serializer.data
        print(f'\nUser: {data["email"]}')
        print(f'  Primary Group ID: {data.get("primary_group_id")}')
        print(f'  Primary Group Name: {data.get("primary_group_name")}')
        groups = data.get('groups', [])
        print(f'  Groups ({len(groups)}):')
        for group in groups:
            print(f'    - {group["name"]} (ID: {group["id"]}, Primary: {group["is_primary"]})')
