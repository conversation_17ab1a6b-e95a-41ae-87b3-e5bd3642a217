import { useAuth } from '../contexts/AuthContext';

// Map frontend permission names to backend permission codenames
const PERMISSION_MAPPING = {
  // Dashboard
  'view_dashboard': ['view_citizen', 'view_idcard', 'view_user'], // If user can view any of these, they can see dashboard

  // Citizens
  'view_citizens': ['view_citizen'],
  'manage_citizens': ['add_citizen', 'change_citizen', 'delete_citizen'],
  'register_citizens': ['add_citizen'],

  // ID Cards
  'view_idcards': ['view_idcard'],
  'manage_idcards': ['add_idcard', 'change_idcard', 'delete_idcard'],
  'create_idcards': ['add_idcard'],
  'approve_idcards': ['delete_citizen', 'delete_idcard'], // Kebele leaders have delete permissions
  'print_idcards': ['change_idcard'], // Users who can change ID cards can print them

  // Tenants
  'view_tenants': ['view_tenant'],
  'manage_tenants': ['add_tenant', 'change_tenant', 'delete_tenant'],
  'create_tenants': ['add_tenant'],
  'edit_tenants': ['change_tenant'],
  'delete_tenants': ['delete_tenant'],

  // Users
  'manage_users': ['add_user', 'change_user', 'view_user'],

  // Reports (if user has approval permissions, they can view reports)
  'view_reports': ['delete_citizen', 'delete_idcard', 'view_tenant'],

  // Workflows
  'view_workflows': ['view_citizenclearancerequest', 'view_citizentransferrequest', 'view_workflowlog'],

  // System settings (only for high-level admins)
  'system_settings': ['view_tenant', 'change_tenant']
};

// Legacy role-based permissions (fallback for when user permissions are not available)
const ROLE_PERMISSIONS = {
  superadmin: [
    'view_dashboard',
    'manage_tenants',
    'view_tenants',
    'create_tenants',
    'edit_tenants',
    'delete_tenants',
    'view_citizens',
    'manage_citizens',
    'register_citizens',
    'view_idcards',
    'manage_idcards',
    'create_idcards',
    'approve_idcards',
    'print_idcards',
    'view_reports',
    'manage_users',
    'view_workflows',
    'system_settings'
  ],
  city_admin: [
    'view_dashboard',
    'view_citizens',
    'manage_citizens',
    'register_citizens',
    'view_idcards',
    'manage_idcards',
    'create_idcards',
    'approve_idcards',
    'print_idcards',
    'view_reports',
    'manage_users', // Can manage users in their city
    'view_workflows'
  ],
  subcity_admin: [
    'view_dashboard',
    'view_citizens',
    'manage_citizens',
    'register_citizens',
    'view_idcards',
    'manage_idcards',
    'create_idcards',
    'approve_idcards',
    'print_idcards',
    'view_reports',
    'manage_users', // Can manage users in their subcity
    'view_workflows'
  ],
  kebele_admin: [
    'view_dashboard',
    'view_citizens',
    'manage_citizens',
    'register_citizens',
    'view_idcards',
    'manage_idcards',
    'create_idcards',
    'approve_idcards',
    'view_reports',
    'manage_users', // Can manage users in their kebele
    'view_workflows'
  ],
  kebele_leader: [
    'view_dashboard',
    'view_citizens',
    'view_idcards',
    'approve_idcards',
    'transfer_citizens',
    'view_reports',
    'view_workflows'
    // Note: Kebele leaders can approve and transfer but cannot create citizens/ID cards or manage users
  ],
  clerk: [
    'view_dashboard',
    'view_citizens',
    'register_citizens',
    'view_idcards',
    'create_idcards'
    // Note: Clerks should NOT have access to:
    // - view_reports (only leaders and admins)
    // - manage_users (only admins)
    // - approve_idcards (only leaders and admins)
    // - print_idcards (only subcity admins and above)
    // - view_tenants (only superadmin)
    // - cross-tenant views (only subcity admins and above)
  ]
};

// Define navigation items with required permissions
export const NAVIGATION_ITEMS = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    path: '/dashboard',
    icon: 'Dashboard',
    permission: 'view_dashboard'
  },
  {
    id: 'citizens',
    label: 'Citizens',
    path: '/citizens',
    icon: 'People',
    permission: 'view_citizens',
    children: [
      {
        id: 'citizens-list',
        label: 'Citizens List',
        path: '/citizens',
        permission: 'view_citizens'
      },
      {
        id: 'citizens-register',
        label: 'Register Citizen',
        path: '/citizens/register',
        permission: 'register_citizens'
      }
    ]
  },
  {
    id: 'idcards',
    label: 'ID Cards',
    path: '/idcards',
    icon: 'Badge',
    permission: 'view_idcards',
    children: [
      {
        id: 'idcards-list',
        label: 'ID Cards List',
        path: '/idcards',
        permission: 'view_idcards'
      },
      {
        id: 'idcards-pending',
        label: 'Pending Approval',
        path: '/idcards/pending',
        permission: 'approve_idcards'
      }
    ]
  },
  {
    id: 'tenants',
    label: 'Tenants',
    path: '/tenants',
    icon: 'Business',
    permission: 'view_tenants',
    children: [
      {
        id: 'tenants-list',
        label: 'Tenants List',
        path: '/tenants',
        permission: 'view_tenants'
      },
      {
        id: 'tenants-register',
        label: 'Register Tenant',
        path: '/tenants/register',
        permission: 'create_tenants'
      }
    ]
  },
  {
    id: 'reports',
    label: 'Reports',
    path: '/reports',
    icon: 'Assessment',
    permission: 'view_reports'
  },
  {
    id: 'users',
    label: 'Users',
    path: '/users',
    icon: 'SupervisorAccount',
    permission: 'manage_users'
  },
  {
    id: 'workflows',
    label: 'Workflows',
    path: '/workflows',
    icon: 'Timeline',
    permission: 'view_workflows'
  },
  {
    id: 'settings',
    label: 'Settings',
    path: '/settings',
    icon: 'Settings',
    permission: 'system_settings'
  }
];

export const usePermissions = () => {
  const { user } = useAuth();

  const hasPermission = (permission) => {
    if (!user) return false;

    // Super admin has all permissions
    if (user.is_superuser || user.role === 'superadmin') return true;

    // Use actual permissions from backend if available
    if (user.permissions && Array.isArray(user.permissions)) {
      const requiredPermissions = PERMISSION_MAPPING[permission] || [permission];

      // Check if user has any of the required backend permissions
      const userPermissionCodes = user.permissions.map(p => p.codename);
      return requiredPermissions.some(reqPerm => userPermissionCodes.includes(reqPerm));
    }

    // Fallback to role-based permissions if user.permissions not available
    if (user.role) {
      const userPermissions = ROLE_PERMISSIONS[user.role] || [];
      return userPermissions.includes(permission);
    }

    return false;
  };

  const hasAnyPermission = (permissions) => {
    return permissions.some(permission => hasPermission(permission));
  };

  const getFilteredNavigationItems = () => {
    return NAVIGATION_ITEMS.filter(item => {
      // Check if user has permission for main item
      if (!hasPermission(item.permission)) return false;

      // Filter children based on permissions
      if (item.children) {
        item.children = item.children.filter(child => hasPermission(child.permission));
      }

      return true;
    });
  };

  const canAccess = (path) => {
    const item = NAVIGATION_ITEMS.find(nav =>
      nav.path === path ||
      (nav.children && nav.children.some(child => child.path === path))
    );

    if (!item) return true; // Allow access to unprotected routes

    if (item.children) {
      const childItem = item.children.find(child => child.path === path);
      return childItem ? hasPermission(childItem.permission) : hasPermission(item.permission);
    }

    return hasPermission(item.permission);
  };

  return {
    hasPermission,
    hasAnyPermission,
    getFilteredNavigationItems,
    canAccess,
    userRole: user?.role,
    isAdmin: user?.role?.includes('admin') || user?.is_superuser,
    isSuperAdmin: user?.is_superuser || user?.role === 'superadmin'
  };
};
