from rest_framework import serializers
from django.contrib.auth.models import Group, Permission
from django.contrib.auth import get_user_model
from .models_groups import TenantGroup, GroupMembership, GroupTemplate
from tenants.models import Tenant

User = get_user_model()


class PermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Permission
        fields = ['id', 'codename', 'name', 'content_type']


class TenantGroupSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='group.name', read_only=True)
    permissions = PermissionSerializer(source='group.permissions', many=True, read_only=True)
    users_count = serializers.ReadOnlyField()
    permissions_count = serializers.ReadOnlyField()
    tenant_name = serializers.CharField(source='tenant.name', read_only=True)
    
    class Meta:
        model = TenantGroup
        fields = [
            'id', 'name', 'description', 'group_type', 'level', 'is_active',
            'is_system_group', 'tenant', 'tenant_name', 'permissions', 
            'users_count', 'permissions_count', 'created_at', 'updated_at'
        ]


class GroupMembershipSerializer(serializers.ModelSerializer):
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_name = serializers.SerializerMethodField()
    group_name = serializers.CharField(source='group.name', read_only=True)
    assigned_by_email = serializers.CharField(source='assigned_by.email', read_only=True)
    
    class Meta:
        model = GroupMembership
        fields = [
            'id', 'user', 'user_email', 'user_name', 'group', 'group_name',
            'assigned_by', 'assigned_by_email', 'assigned_at', 'reason',
            'is_primary', 'is_active', 'expires_at'
        ]
    
    def get_user_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}".strip()


class GroupTemplateSerializer(serializers.ModelSerializer):
    permissions = PermissionSerializer(many=True, read_only=True)
    permissions_count = serializers.SerializerMethodField()
    
    class Meta:
        model = GroupTemplate
        fields = [
            'id', 'name', 'description', 'group_type', 'level',
            'is_system_template', 'tenant_types', 'permissions',
            'permissions_count', 'created_at', 'updated_at'
        ]
    
    def get_permissions_count(self, obj):
        return obj.permissions.count()


class GroupManagementSerializer(serializers.Serializer):
    """
    Serializer for group management operations.
    """
    action = serializers.ChoiceField(choices=[
        'create_group',
        'add_user_to_group',
        'remove_user_from_group',
        'assign_permissions',
        'remove_permissions',
        'create_from_template'
    ])
    
    # Group creation fields
    group_name = serializers.CharField(required=False)
    description = serializers.CharField(required=False, allow_blank=True)
    group_type = serializers.ChoiceField(
        choices=[
            ('administrative', 'Administrative'),
            ('operational', 'Operational'),
            ('functional', 'Functional'),
            ('custom', 'Custom')
        ],
        required=False,
        default='custom'
    )
    level = serializers.IntegerField(required=False, default=0)
    tenant_id = serializers.IntegerField(required=False)
    
    # User assignment fields
    user_email = serializers.EmailField(required=False)
    group_id = serializers.IntegerField(required=False)
    is_primary = serializers.BooleanField(required=False, default=False)
    reason = serializers.CharField(required=False, allow_blank=True)
    
    # Permission assignment fields
    permission_codenames = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        default=list
    )
    
    # Template fields
    template_id = serializers.IntegerField(required=False)
    
    def validate(self, data):
        action = data.get('action')
        
        if action == 'create_group':
            if not data.get('group_name'):
                raise serializers.ValidationError("group_name is required for create_group action")
        
        elif action in ['add_user_to_group', 'remove_user_from_group']:
            if not data.get('user_email') or not data.get('group_id'):
                raise serializers.ValidationError("user_email and group_id are required")
        
        elif action in ['assign_permissions', 'remove_permissions']:
            if not data.get('group_id') or not data.get('permission_codenames'):
                raise serializers.ValidationError("group_id and permission_codenames are required")
        
        elif action == 'create_from_template':
            if not data.get('template_id') or not data.get('group_name'):
                raise serializers.ValidationError("template_id and group_name are required")
        
        return data
    
    def save(self):
        action = self.validated_data['action']
        user = self.context['request'].user
        
        if action == 'create_group':
            return self._create_group(user)
        elif action == 'add_user_to_group':
            return self._add_user_to_group(user)
        elif action == 'remove_user_from_group':
            return self._remove_user_from_group(user)
        elif action == 'assign_permissions':
            return self._assign_permissions(user)
        elif action == 'remove_permissions':
            return self._remove_permissions(user)
        elif action == 'create_from_template':
            return self._create_from_template(user)
    
    def _create_group(self, creator):
        """Create a new group"""
        group_name = self.validated_data['group_name']
        description = self.validated_data.get('description', '')
        group_type = self.validated_data.get('group_type', 'custom')
        level = self.validated_data.get('level', 0)
        tenant_id = self.validated_data.get('tenant_id')
        
        # Get tenant if specified
        tenant = None
        if tenant_id:
            try:
                tenant = Tenant.objects.get(id=tenant_id)
            except Tenant.DoesNotExist:
                raise serializers.ValidationError("Tenant not found")
        
        # Create Django Group
        django_group = Group.objects.create(name=group_name)
        
        # Create TenantGroup
        tenant_group = TenantGroup.objects.create(
            group=django_group,
            tenant=tenant,
            description=description,
            group_type=group_type,
            level=level,
            created_by=creator
        )
        
        return {
            'action': 'create_group',
            'group': {
                'id': tenant_group.id,
                'name': group_name,
                'description': description,
                'group_type': group_type,
                'level': level,
                'tenant': tenant.name if tenant else None
            },
            'message': f"Group '{group_name}' created successfully"
        }
    
    def _add_user_to_group(self, assigner):
        """Add user to group"""
        user_email = self.validated_data['user_email']
        group_id = self.validated_data['group_id']
        is_primary = self.validated_data.get('is_primary', False)
        reason = self.validated_data.get('reason', '')
        
        try:
            user = User.objects.get(email=user_email, is_active=True)
            group = TenantGroup.objects.get(id=group_id, is_active=True)
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found")
        except TenantGroup.DoesNotExist:
            raise serializers.ValidationError("Group not found")
        
        # Add user to group
        membership = user.add_to_group(
            group=group,
            assigned_by=assigner,
            reason=reason,
            is_primary=is_primary
        )
        
        return {
            'action': 'add_user_to_group',
            'user': user.email,
            'group': group.name,
            'is_primary': is_primary,
            'message': f"User '{user.email}' added to group '{group.name}'"
        }
    
    def _remove_user_from_group(self, remover):
        """Remove user from group"""
        user_email = self.validated_data['user_email']
        group_id = self.validated_data['group_id']
        
        try:
            user = User.objects.get(email=user_email, is_active=True)
            group = TenantGroup.objects.get(id=group_id, is_active=True)
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found")
        except TenantGroup.DoesNotExist:
            raise serializers.ValidationError("Group not found")
        
        # Remove user from group
        user.remove_from_group(group)
        
        return {
            'action': 'remove_user_from_group',
            'user': user.email,
            'group': group.name,
            'message': f"User '{user.email}' removed from group '{group.name}'"
        }
    
    def _assign_permissions(self, assigner):
        """Assign permissions to group"""
        group_id = self.validated_data['group_id']
        permission_codenames = self.validated_data['permission_codenames']
        
        try:
            group = TenantGroup.objects.get(id=group_id, is_active=True)
        except TenantGroup.DoesNotExist:
            raise serializers.ValidationError("Group not found")
        
        # Get permissions
        permissions = Permission.objects.filter(codename__in=permission_codenames)
        if permissions.count() != len(permission_codenames):
            found_codenames = set(permissions.values_list('codename', flat=True))
            missing = set(permission_codenames) - found_codenames
            raise serializers.ValidationError(f"Permissions not found: {', '.join(missing)}")
        
        # Assign permissions
        group.group.permissions.add(*permissions)
        
        return {
            'action': 'assign_permissions',
            'group': group.name,
            'permissions_assigned': len(permissions),
            'permissions': list(permissions.values_list('codename', flat=True)),
            'message': f"Assigned {len(permissions)} permissions to group '{group.name}'"
        }
    
    def _remove_permissions(self, remover):
        """Remove permissions from group"""
        group_id = self.validated_data['group_id']
        permission_codenames = self.validated_data['permission_codenames']
        
        try:
            group = TenantGroup.objects.get(id=group_id, is_active=True)
        except TenantGroup.DoesNotExist:
            raise serializers.ValidationError("Group not found")
        
        # Get permissions
        permissions = Permission.objects.filter(codename__in=permission_codenames)
        
        # Remove permissions
        group.group.permissions.remove(*permissions)
        
        return {
            'action': 'remove_permissions',
            'group': group.name,
            'permissions_removed': len(permissions),
            'permissions': list(permissions.values_list('codename', flat=True)),
            'message': f"Removed {len(permissions)} permissions from group '{group.name}'"
        }
    
    def _create_from_template(self, creator):
        """Create group from template"""
        template_id = self.validated_data['template_id']
        group_name = self.validated_data['group_name']
        tenant_id = self.validated_data.get('tenant_id')
        
        try:
            template = GroupTemplate.objects.get(id=template_id)
        except GroupTemplate.DoesNotExist:
            raise serializers.ValidationError("Template not found")
        
        # Get tenant if specified
        tenant = None
        if tenant_id:
            try:
                tenant = Tenant.objects.get(id=tenant_id)
            except Tenant.DoesNotExist:
                raise serializers.ValidationError("Tenant not found")
        
        # Create group from template
        tenant_group = template.create_group(
            name=group_name,
            tenant=tenant,
            created_by=creator
        )
        
        return {
            'action': 'create_from_template',
            'template': template.name,
            'group': {
                'id': tenant_group.id,
                'name': group_name,
                'permissions_count': tenant_group.permissions_count
            },
            'message': f"Group '{group_name}' created from template '{template.name}'"
        }
