import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Alert,
  CircularProgress,
} from '@mui/material';
import { useFormik } from 'formik';
import * as yup from 'yup';
import axios from '../../utils/axios';
import { getStandardMenuProps, createSelectChangeHandler } from '../../utils/dropdownUtils';

// Mock data for tenants
const mockTenants = [
  {
    id: 1,
    name: 'Addis Ababa',
    type: 'city',
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    type: 'subcity',
    parent: 1,
  },
  {
    id: 3,
    name: '<PERSON><PERSON> 01',
    type: 'kebele',
    parent: 2,
  },
];

const validationSchema = yup.object({
  email: yup.string().email('Enter a valid email').required('Email is required'),
  username: yup.string().required('Username is required'),
  password: yup.string()
    .min(8, 'Password should be of minimum 8 characters length')
    .required('Password is required'),
  password2: yup.string()
    .oneOf([yup.ref('password'), null], 'Passwords must match')
    .required('Confirm password is required'),
  first_name: yup.string().required('First name is required'),
  last_name: yup.string().required('Last name is required'),
  role: yup.string().required('Role is required'),
  tenant: yup.number().when('role', {
    is: (role) => role !== 'superadmin',
    then: () => yup.number().required('Tenant is required'),
    otherwise: () => yup.number().nullable(),
  }),
});

const UserCreate = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [tenants, setTenants] = useState([]);
  const [availableTenants, setAvailableTenants] = useState([]);

  useEffect(() => {
    // In a real implementation, this would fetch data from the API
    // For now, we'll just use mock data
    setTenants(mockTenants);
  }, []);

  const formik = useFormik({
    initialValues: {
      email: '',
      username: '',
      password: '',
      password2: '',
      first_name: '',
      last_name: '',
      role: '',
      tenant: '',
      is_active: true,
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        setError('');

        // In a real implementation, this would call an API endpoint
        // For now, we'll just simulate a successful request
        await new Promise(resolve => setTimeout(resolve, 1000));

        setSuccess(true);
        setTimeout(() => {
          navigate('/users');
        }, 1500);
      } catch (err) {
        setError(err.response?.data?.detail || 'Failed to create user. Please try again.');
      } finally {
        setLoading(false);
      }
    },
  });

  // Update available tenants based on selected role
  useEffect(() => {
    if (formik.values.role === 'city_admin') {
      // City admins can only be assigned to city tenants
      setAvailableTenants(tenants.filter(tenant => tenant.type === 'city'));
    } else if (formik.values.role === 'subcity_admin') {
      // Subcity admins can only be assigned to subcity tenants
      setAvailableTenants(tenants.filter(tenant => tenant.type === 'subcity'));
    } else if (formik.values.role === 'kebele_admin' || formik.values.role === 'clerk') {
      // Kebele admins and clerks can only be assigned to kebele tenants
      setAvailableTenants(tenants.filter(tenant => tenant.type === 'kebele'));
    } else {
      setAvailableTenants([]);
      formik.setFieldValue('tenant', '');
    }
  }, [formik.values.role, tenants]);

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Create New User
        </Typography>
        <Button
          variant="outlined"
          onClick={() => navigate('/users')}
        >
          Cancel
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          User created successfully!
        </Alert>
      )}

      <form onSubmit={formik.handleSubmit}>
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              User Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="first_name"
                  name="first_name"
                  label="First Name"
                  value={formik.values.first_name}
                  onChange={formik.handleChange}
                  error={formik.touched.first_name && Boolean(formik.errors.first_name)}
                  helperText={formik.touched.first_name && formik.errors.first_name}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="last_name"
                  name="last_name"
                  label="Last Name"
                  value={formik.values.last_name}
                  onChange={formik.handleChange}
                  error={formik.touched.last_name && Boolean(formik.errors.last_name)}
                  helperText={formik.touched.last_name && formik.errors.last_name}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="email"
                  name="email"
                  label="Email Address"
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  error={formik.touched.email && Boolean(formik.errors.email)}
                  helperText={formik.touched.email && formik.errors.email}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="username"
                  name="username"
                  label="Username"
                  value={formik.values.username}
                  onChange={formik.handleChange}
                  error={formik.touched.username && Boolean(formik.errors.username)}
                  helperText={formik.touched.username && formik.errors.username}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="password"
                  name="password"
                  label="Password"
                  type="password"
                  value={formik.values.password}
                  onChange={formik.handleChange}
                  error={formik.touched.password && Boolean(formik.errors.password)}
                  helperText={formik.touched.password && formik.errors.password}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="password2"
                  name="password2"
                  label="Confirm Password"
                  type="password"
                  value={formik.values.password2}
                  onChange={formik.handleChange}
                  error={formik.touched.password2 && Boolean(formik.errors.password2)}
                  helperText={formik.touched.password2 && formik.errors.password2}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={formik.touched.role && Boolean(formik.errors.role)}>
                  <InputLabel id="role-label">Role</InputLabel>
                  <Select
                    labelId="role-label"
                    id="role"
                    name="role"
                    value={formik.values.role}
                    label="Role"
                    onChange={createSelectChangeHandler('role', formik.handleChange)}
                    MenuProps={getStandardMenuProps()}
                  >
                    <MenuItem value="superadmin">Super Admin</MenuItem>
                    <MenuItem value="city_admin">City Admin</MenuItem>
                    <MenuItem value="subcity_admin">Subcity Admin</MenuItem>
                    <MenuItem value="kebele_admin">Kebele Admin</MenuItem>
                    <MenuItem value="clerk">Clerk</MenuItem>
                  </Select>
                  {formik.touched.role && formik.errors.role && (
                    <FormHelperText>{formik.errors.role}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              {formik.values.role && formik.values.role !== 'superadmin' && (
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth error={formik.touched.tenant && Boolean(formik.errors.tenant)}>
                    <InputLabel id="tenant-label">Tenant</InputLabel>
                    <Select
                      labelId="tenant-label"
                      id="tenant"
                      name="tenant"
                      value={formik.values.tenant}
                      label="Tenant"
                      onChange={createSelectChangeHandler('tenant', formik.handleChange)}
                      MenuProps={getStandardMenuProps()}
                    >
                      {availableTenants.map((tenant) => (
                        <MenuItem key={tenant.id} value={tenant.id}>
                          {tenant.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.tenant && formik.errors.tenant && (
                      <FormHelperText>{formik.errors.tenant}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              )}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel id="is-active-label">Status</InputLabel>
                  <Select
                    labelId="is-active-label"
                    id="is_active"
                    name="is_active"
                    value={formik.values.is_active}
                    label="Status"
                    onChange={createSelectChangeHandler('is_active', formik.handleChange)}
                    MenuProps={getStandardMenuProps()}
                  >
                    <MenuItem value={true}>Active</MenuItem>
                    <MenuItem value={false}>Inactive</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
          <Button
            type="submit"
            variant="contained"
            size="large"
            disabled={loading}
            sx={{ minWidth: 150 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Create User'}
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default UserCreate;
