import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  IconButton,
  Tooltip,
  Alert,
  TextField,
  InputAdornment,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Search as SearchIcon,
  CheckCircle as ApprovedIcon,
  Pending as PendingIcon,
  Cancel as RejectedIcon,
  Print as PrintIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import { getSecurityPatternClass, getPatternDescription } from '../../utils/securityPatterns';
import '../../styles/securityPatterns.css';

const SubcityIDCardsList = () => {
  const [idCards, setIdCards] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const { user } = useAuth();
  const navigate = useNavigate();

  const pageSize = 10;

  const getTenantId = () => {
    console.log('🔍 Getting tenant ID...');

    // Get tenant ID from multiple sources for reliability
    let tenantId = user?.tenant_id;
    console.log('🔍 From user context:', tenantId);

    if (!tenantId) {
      // Fallback: Get from localStorage user object
      try {
        const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
        tenantId = storedUser.tenant_id;
        console.log('🔍 From localStorage user:', tenantId);
      } catch (e) {
        console.warn('Could not parse stored user data');
      }
    }

    if (!tenantId) {
      // Fallback: Get from JWT token
      try {
        const accessToken = localStorage.getItem('accessToken');
        if (accessToken) {
          const base64Url = accessToken.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
          const tokenData = JSON.parse(jsonPayload);
          tenantId = tokenData.tenant_id;
          console.log('🔍 From JWT token:', tenantId);
        }
      } catch (e) {
        console.warn('Could not decode JWT token');
      }
    }

    if (!tenantId) {
      console.error('❌ No tenant ID found in any source');
      return null;
    }

    console.log('✅ Final tenant ID:', tenantId);
    return tenantId;
  };

  useEffect(() => {
    fetchIDCards();
  }, [page, search, statusFilter]);

  const fetchIDCards = async () => {
    try {
      setLoading(true);
      const tenantId = getTenantId();

      if (!tenantId) {
        setError('No tenant selected');
        return;
      }

      console.log('🔍 Fetching cross-tenant ID cards for subcity tenant:', tenantId);

      const params = new URLSearchParams({
        page: page.toString(),
        page_size: pageSize.toString(),
      });

      if (search.trim()) {
        params.append('search', search.trim());
      }

      if (statusFilter) {
        params.append('status', statusFilter);
      }

      const response = await axios.get(`/api/tenants/${tenantId}/idcards/cross_tenant_list/?${params}`);

      console.log('📋 Cross-tenant ID cards response:', response.data);
      setIdCards(response.data.results || []);
      setTotalCount(response.data.count || 0);
      setTotalPages(response.data.total_pages || 1);
    } catch (error) {
      console.error('Error fetching cross-tenant ID cards:', error);
      setError(error.response?.data?.error || 'Failed to fetch ID cards');
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (event) => {
    setSearch(event.target.value);
    setPage(1); // Reset to first page when searching
  };

  const handleStatusFilterChange = (event) => {
    setStatusFilter(event.target.value);
    setPage(1); // Reset to first page when filtering
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleViewIDCard = (cardId, kebeleId) => {
    // Navigate to the ID card view in the kebele tenant context
    navigate(`/tenants/${kebeleId}/idcards/${cardId}`);
  };

  const getCitizenName = (card) => {
    return card.citizen_name ||
           [card.citizen_first_name, card.citizen_middle_name, card.citizen_last_name]
             .filter(Boolean)
             .join(' ') ||
           'Unknown Citizen';
  };

  const getStatusChip = (status) => {
    const statusConfig = {
      'draft': {
        icon: <PendingIcon />,
        label: 'Draft',
        color: 'default',
      },
      'pending_approval': {
        icon: <PendingIcon />,
        label: 'Pending Kebele Approval',
        color: 'warning',
      },
      'kebele_approved': {
        icon: <ApprovedIcon />,
        label: 'Kebele Approved - Pending Subcity',
        color: 'info',
      },
      'approved': {
        icon: <ApprovedIcon />,
        label: 'Fully Approved - Ready for Printing',
        color: 'success',
      },
      'rejected': {
        icon: <RejectedIcon />,
        label: 'Rejected',
        color: 'error',
      },
      'printed': {
        icon: <PrintIcon />,
        label: 'Printed',
        color: 'info',
      },
      'issued': {
        icon: <ApprovedIcon />,
        label: 'Issued',
        color: 'primary',
      }
    };

    const config = statusConfig[status] || { icon: null, label: status, color: 'default' };

    return (
      <Chip
        icon={config.icon}
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Kebele-Approved ID Cards
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Kebele-Approved ID Cards ({totalCount})
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel>Status Filter</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={handleStatusFilterChange}
                  label="Status Filter"
                >
                  <MenuItem value="">All Kebele-Approved Statuses</MenuItem>
                  <MenuItem value="kebele_approved">Pending Subcity Approval</MenuItem>
                  <MenuItem value="approved">Fully Approved</MenuItem>
                  <MenuItem value="printed">Printed</MenuItem>
                  <MenuItem value="issued">Issued</MenuItem>
                </Select>
              </FormControl>
              <TextField
                placeholder="Search ID cards..."
                value={search}
                onChange={handleSearchChange}
                size="small"
                sx={{ width: 300 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            View and manage ID cards that have been approved by kebele leaders and are ready for subcity review.
          </Typography>

          {idCards.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No ID cards found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {search || statusFilter ? 'Try adjusting your search or filter criteria.' : 'No kebele-approved ID cards found.'}
              </Typography>
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Citizen</TableCell>
                      <TableCell>Kebele</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Security Pattern</TableCell>
                      <TableCell>Created Date</TableCell>
                      <TableCell align="center">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {idCards.map((card) => (
                      <TableRow key={`${card.kebele_tenant.id}-${card.id}`} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar
                              sx={{
                                bgcolor: 'primary.main',
                                width: 40,
                                height: 40,
                                fontSize: '1rem'
                              }}
                            >
                              {getCitizenName(card)?.charAt(0)?.toUpperCase() || 'U'}
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle2" fontWeight="bold">
                                {getCitizenName(card)}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                ID: {card.citizen_digital_id || 'N/A'}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={card.kebele_tenant.name}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          {getStatusChip(card.status)}
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {getPatternDescription(card, user)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatDate(card.created_at)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Tooltip title="View ID Card Details">
                            <IconButton
                              onClick={() => handleViewIDCard(card.id, card.kebele_tenant.id)}
                              size="small"
                              sx={{
                                bgcolor: 'primary.main',
                                color: 'white',
                                '&:hover': { bgcolor: 'primary.dark' }
                              }}
                            >
                              <ViewIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={handlePageChange}
                    color="primary"
                    showFirstButton
                    showLastButton
                  />
                </Box>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default SubcityIDCardsList;
