from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from django.contrib.auth import get_user_model
from tenants.models import Tenant
import jwt


class TenantAwareJWTAuthentication(JWTAuthentication):
    """
    Custom JWT authentication that populates user.tenant from JWT token.
    """
    
    def authenticate(self, request):
        """
        Authenticate the request and populate user.tenant and user.role from JWT token.
        """
        result = super().authenticate(request)

        if result is not None:
            user, validated_token = result

            # Extract information from the token
            tenant_id = validated_token.get('tenant_id')
            role = validated_token.get('role')
            is_superuser = validated_token.get('is_superuser', False)

            # Set role from token (this ensures the role is current)
            # The role in the JWT token is the authoritative source for the current session
            if role:
                user.role = role
                print(f"🔍 TenantAwareJWTAuthentication: Setting user role to '{role}' from JWT token")

            # Set is_superuser from token
            user.is_superuser = is_superuser

            # Set tenant information
            if tenant_id:
                try:
                    tenant = Tenant.objects.get(id=tenant_id)
                    # Set tenant attribute on user object
                    user.tenant = tenant
                except Tenant.DoesNotExist:
                    # If tenant doesn't exist, continue without setting it
                    pass

            return user, validated_token

        return result
