import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Alert,
  Paper,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Grid
} from '@mui/material';
import {
  Print as PrintIcon,
  PictureAsPdf as PdfIcon,
  Book as BookIcon,
  LocationCity as CityIcon,
  Business as SubcityIcon,
  LocationOn as KebeleIcon,
  People as PeopleIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import axios from '../../utils/axios';

const CitizenBookPrintable = () => {
  const { user } = useAuth();
  const [bookData, setBookData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Function to organize flat citizen data into hierarchical structure
  const organizeDataForBook = (data) => {
    if (!data.citizens || data.citizens.length === 0) {
      return {
        ...data,
        subcity_directory: [],
        statistics: {
          ...data.statistics,
          total_kebeles: 0,
          total_male: 0,
          total_female: 0
        }
      };
    }

    // Group citizens by subcity and kebele
    const subcityMap = {};
    let totalMale = 0;
    let totalFemale = 0;

    data.citizens.forEach(citizen => {
      const subcityName = citizen.subcity_name;
      const kebeleName = citizen.kebele_name;

      // Count gender
      if (citizen.gender === 'male') totalMale++;
      if (citizen.gender === 'female') totalFemale++;

      // Initialize subcity if not exists
      if (!subcityMap[subcityName]) {
        subcityMap[subcityName] = {
          subcity_name: subcityName,
          subcity_stats: {
            total_citizens: 0,
            male_count: 0,
            female_count: 0,
            kebeles_count: 0
          },
          kebeles: {}
        };
      }

      // Initialize kebele if not exists
      if (!subcityMap[subcityName].kebeles[kebeleName]) {
        subcityMap[subcityName].kebeles[kebeleName] = {
          kebele_name: kebeleName,
          kebele_stats: {
            total_citizens: 0,
            male_count: 0,
            female_count: 0
          },
          citizens: []
        };
        subcityMap[subcityName].subcity_stats.kebeles_count++;
      }

      // Add citizen to kebele
      subcityMap[subcityName].kebeles[kebeleName].citizens.push(citizen);
      subcityMap[subcityName].kebeles[kebeleName].kebele_stats.total_citizens++;
      if (citizen.gender === 'male') {
        subcityMap[subcityName].kebeles[kebeleName].kebele_stats.male_count++;
      } else if (citizen.gender === 'female') {
        subcityMap[subcityName].kebeles[kebeleName].kebele_stats.female_count++;
      }

      // Update subcity stats
      subcityMap[subcityName].subcity_stats.total_citizens++;
      if (citizen.gender === 'male') {
        subcityMap[subcityName].subcity_stats.male_count++;
      } else if (citizen.gender === 'female') {
        subcityMap[subcityName].subcity_stats.female_count++;
      }
    });

    // Convert to array and sort
    const subcity_directory = Object.values(subcityMap).map(subcity => ({
      ...subcity,
      kebeles: Object.values(subcity.kebeles).sort((a, b) => a.kebele_name.localeCompare(b.kebele_name))
    })).sort((a, b) => a.subcity_name.localeCompare(b.subcity_name));

    // Calculate total kebeles
    const totalKebeles = subcity_directory.reduce((sum, subcity) => sum + subcity.kebeles.length, 0);

    return {
      ...data,
      subcity_directory,
      statistics: {
        ...data.statistics,
        total_kebeles: totalKebeles,
        total_male: totalMale,
        total_female: totalFemale
      },
      city_info: {
        ...data.city_info,
        generated_date: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        generated_time: new Date().toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        })
      }
    };
  };

  useEffect(() => {
    if (user) {
      fetchBookData();
    }
  }, [user]);

  const fetchBookData = async () => {
    try {
      setLoading(true);

      // Get tenant ID
      let tenantId = user?.tenant_id;
      if (!tenantId) {
        const token = localStorage.getItem('accessToken');
        if (token) {
          const payload = JSON.parse(atob(token.split('.')[1]));
          tenantId = payload.tenant_id;
        }
      }

      if (!tenantId) {
        throw new Error('No tenant ID available');
      }

      const response = await axios.get(`/api/tenants/${tenantId}/citizens/citizen-book/data/`);

      // Organize flat citizen data into hierarchical structure for the book
      const organizedData = organizeDataForBook(response.data);
      setBookData(organizedData);
      setError(null);
    } catch (error) {
      console.error('Error fetching citizen book data:', error);
      setError(`Failed to load citizen book: ${error.response?.data?.error || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExportPDF = () => {
    // This would integrate with a PDF generation library
    // For now, we'll use the browser's print to PDF functionality
    window.print();
  };

  if (!user || loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <CircularProgress size={60} />
          <Typography variant="h6" className="mt-4">
            Generating Citizen Book...
          </Typography>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert severity="error" className="m-4">
        {error}
      </Alert>
    );
  }

  if (!bookData) {
    return (
      <Alert severity="info" className="m-4">
        No citizen book data available
      </Alert>
    );
  }

  return (
    <div className="citizen-book-container">
      {/* Print Controls - Hidden when printing */}
      <div className="print-controls no-print p-4 bg-gray-100 border-b">
        <div className="flex items-center justify-between">
          <Typography variant="h5" className="font-bold">
            Citizen Book - Print Preview
          </Typography>
          <div className="space-x-2">
            <Button
              variant="contained"
              startIcon={<PrintIcon />}
              onClick={handlePrint}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Print Book
            </Button>
            <Button
              variant="outlined"
              startIcon={<PdfIcon />}
              onClick={handleExportPDF}
              className="border-blue-600 text-blue-600 hover:bg-blue-50"
            >
              Export PDF
            </Button>
          </div>
        </div>
      </div>

      {/* Book Content - This will be printed */}
      <div className="book-content bg-white">
        {/* Cover Page */}
        <div className="cover-page page-break-after text-center p-12">
          <div className="cover-header mb-8">
            <CityIcon style={{ fontSize: '4rem', color: '#ff8f00' }} />
            <Typography variant="h2" className="font-bold mt-4" style={{ color: '#ff8f00' }}>
              CITIZEN DIRECTORY
            </Typography>
            <Typography variant="h3" className="font-semibold mt-2 text-gray-700">
              {bookData.city_info?.name} City
            </Typography>
          </div>

          <div className="cover-stats mt-12 p-8 bg-gray-50 rounded-lg">
            <Grid container spacing={4} justifyContent="center">
              <Grid item xs={6} md={3}>
                <div className="text-center">
                  <Typography variant="h3" className="font-bold text-blue-600">
                    {bookData.statistics?.total_citizens?.toLocaleString() || 0}
                  </Typography>
                  <Typography variant="h6" className="text-gray-600">
                    Total Citizens
                  </Typography>
                </div>
              </Grid>
              <Grid item xs={6} md={3}>
                <div className="text-center">
                  <Typography variant="h3" className="font-bold text-purple-600">
                    {bookData.statistics?.total_subcities || 0}
                  </Typography>
                  <Typography variant="h6" className="text-gray-600">
                    Sub-Cities
                  </Typography>
                </div>
              </Grid>
              <Grid item xs={6} md={3}>
                <div className="text-center">
                  <Typography variant="h3" className="font-bold text-green-600">
                    {bookData.statistics?.total_kebeles || 0}
                  </Typography>
                  <Typography variant="h6" className="text-gray-600">
                    Kebeles
                  </Typography>
                </div>
              </Grid>
              <Grid item xs={6} md={3}>
                <div className="text-center">
                  <Typography variant="h6" className="font-bold text-orange-600">
                    {bookData.statistics?.total_male || 0}M / {bookData.statistics?.total_female || 0}F
                  </Typography>
                  <Typography variant="h6" className="text-gray-600">
                    Gender Ratio
                  </Typography>
                </div>
              </Grid>
            </Grid>
          </div>

          <div className="cover-footer mt-12">
            <Typography variant="h6" className="text-gray-600">
              Generated on {bookData.city_info?.generated_date}
            </Typography>
            <Typography variant="body1" className="text-gray-500 mt-2">
              Official Citizen Directory - {bookData.city_info?.generated_time}
            </Typography>
          </div>
        </div>

        {/* Table of Contents */}
        <div className="table-of-contents page-break-after p-8">
          <Typography variant="h3" className="font-bold mb-6 text-center" style={{ color: '#ff8f00' }}>
            TABLE OF CONTENTS
          </Typography>
          
          <div className="toc-content">
            {bookData.subcity_directory?.map((subcity, index) => (
              <div key={subcity.subcity_name} className="toc-entry mb-4">
                <Typography variant="h5" className="font-semibold mb-2 flex items-center">
                  <SubcityIcon className="mr-2" style={{ color: '#ff8f00' }} />
                  {subcity.subcity_name}
                  <span className="ml-auto text-sm text-gray-500">
                    {subcity.subcity_stats.total_citizens} citizens
                  </span>
                </Typography>
                
                <div className="kebele-list ml-8">
                  {subcity.kebeles?.map((kebele) => (
                    <div key={kebele.kebele_name} className="flex justify-between items-center py-1">
                      <Typography variant="body1" className="flex items-center">
                        <KebeleIcon className="mr-2 text-sm" style={{ color: '#666' }} />
                        {kebele.kebele_name}
                      </Typography>
                      <Typography variant="body2" className="text-gray-500">
                        {kebele.kebele_stats.total_citizens} citizens
                      </Typography>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Citizen Directory by Subcity and Kebele */}
        {bookData.subcity_directory?.map((subcity) => (
          <div key={subcity.subcity_name} className="subcity-section">
            {/* Subcity Header */}
            <div className="subcity-header page-break-before p-6 bg-orange-50 border-l-4 border-orange-500">
              <Typography variant="h3" className="font-bold flex items-center" style={{ color: '#ff8f00' }}>
                <SubcityIcon className="mr-3" />
                {subcity.subcity_name} Sub-City
              </Typography>
              <div className="subcity-stats mt-4 grid grid-cols-4 gap-4">
                <div className="text-center">
                  <Typography variant="h5" className="font-bold text-blue-600">
                    {subcity.subcity_stats.total_citizens}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">Total Citizens</Typography>
                </div>
                <div className="text-center">
                  <Typography variant="h5" className="font-bold text-green-600">
                    {subcity.subcity_stats.male_count}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">Male</Typography>
                </div>
                <div className="text-center">
                  <Typography variant="h5" className="font-bold text-pink-600">
                    {subcity.subcity_stats.female_count}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">Female</Typography>
                </div>
                <div className="text-center">
                  <Typography variant="h5" className="font-bold text-purple-600">
                    {subcity.subcity_stats.kebeles_count}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">Kebeles</Typography>
                </div>
              </div>
            </div>

            {/* Kebeles in this Subcity */}
            {subcity.kebeles?.map((kebele) => (
              <div key={kebele.kebele_name} className="kebele-section mt-6">
                {/* Kebele Header */}
                <div className="kebele-header p-4 bg-blue-50 border-l-4 border-blue-500">
                  <Typography variant="h4" className="font-semibold flex items-center text-blue-700">
                    <KebeleIcon className="mr-2" />
                    {kebele.kebele_name} Kebele
                  </Typography>
                  <div className="kebele-stats mt-2 flex space-x-6">
                    <span className="text-sm">
                      <strong>Total:</strong> {kebele.kebele_stats.total_citizens}
                    </span>
                    <span className="text-sm">
                      <strong>Male:</strong> {kebele.kebele_stats.male_count}
                    </span>
                    <span className="text-sm">
                      <strong>Female:</strong> {kebele.kebele_stats.female_count}
                    </span>
                  </div>
                </div>

                {/* Enhanced Citizens Table */}
                {kebele.citizens?.length > 0 ? (
                  <div className="citizens-table-enhanced mt-4 relative">
                    {/* Watermark Background */}
                    <div className="watermark-bg"></div>

                    <table className="w-full border-collapse border-2 border-gray-800 bg-white bg-opacity-95 relative z-10">
                      <thead>
                        <tr className="bg-gradient-to-r from-orange-100 to-orange-200 border-b-2 border-gray-800">
                          <th className="border border-gray-600 p-3 text-xs font-bold text-gray-800">#</th>
                          <th className="border border-gray-600 p-3 text-xs font-bold text-gray-800">Photo</th>
                          <th className="border border-gray-600 p-3 text-xs font-bold text-gray-800">Full Name</th>
                          <th className="border border-gray-600 p-3 text-xs font-bold text-gray-800">Digital ID</th>
                          <th className="border border-gray-600 p-3 text-xs font-bold text-gray-800">Date of Birth</th>
                          <th className="border border-gray-600 p-3 text-xs font-bold text-gray-800">Age</th>
                          <th className="border border-gray-600 p-3 text-xs font-bold text-gray-800">Gender</th>
                          <th className="border border-gray-600 p-3 text-xs font-bold text-gray-800">Phone</th>
                          <th className="border border-gray-600 p-3 text-xs font-bold text-gray-800">Registration Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        {kebele.citizens.map((citizen, index) => {
                          // Determine photo source
                          const getPhotoSrc = () => {
                            if (citizen.photo_base64) {
                              // Handle base64 photo
                              if (citizen.photo_base64.startsWith('data:')) {
                                return citizen.photo_base64;
                              } else {
                                return `data:image/jpeg;base64,${citizen.photo_base64}`;
                              }
                            } else if (citizen.photo_url) {
                              // Handle photo URL
                              return citizen.photo_url;
                            }
                            return null;
                          };

                          const photoSrc = getPhotoSrc();

                          // Calculate age
                          const calculateAge = (dateOfBirth) => {
                            if (!dateOfBirth) return 'N/A';
                            const today = new Date();
                            const birthDate = new Date(dateOfBirth);
                            let age = today.getFullYear() - birthDate.getFullYear();
                            const monthDiff = today.getMonth() - birthDate.getMonth();
                            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                              age--;
                            }
                            return age;
                          };

                          return (
                            <tr key={citizen.id} className={`${index % 2 === 0 ? 'bg-white bg-opacity-90' : 'bg-orange-50 bg-opacity-90'} hover:bg-orange-100 transition-colors`}>
                              {/* Row Number */}
                              <td className="border border-gray-600 p-2 text-center font-bold text-xs text-gray-700">
                                {index + 1}
                              </td>

                              {/* Photo */}
                              <td className="border border-gray-600 p-2 text-center">
                                <div className="citizen-photo-enhanced mx-auto" style={{width: '45px', height: '45px'}}>
                                  {photoSrc ? (
                                    <img
                                      src={photoSrc}
                                      alt={citizen.full_name}
                                      className="w-full h-full object-cover rounded-md border-2 border-gray-400 shadow-sm"
                                      style={{objectFit: 'cover'}}
                                    />
                                  ) : (
                                    <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 rounded-md border-2 border-gray-400 flex items-center justify-center shadow-sm">
                                      <PersonIcon style={{fontSize: '20px', color: '#666'}} />
                                    </div>
                                  )}
                                </div>
                              </td>

                              {/* Full Name */}
                              <td className="border border-gray-600 p-2">
                                <div className="font-bold text-xs text-gray-800 leading-tight">
                                  {citizen.full_name}
                                </div>
                                {citizen.first_name_am && (
                                  <div className="text-xs text-orange-600 mt-1 italic">
                                    {citizen.first_name_am} {citizen.middle_name_am} {citizen.last_name_am}
                                  </div>
                                )}
                              </td>

                              {/* Digital ID */}
                              <td className="border border-gray-600 p-2">
                                <div className="text-xs font-mono font-bold text-blue-800 bg-blue-50 px-2 py-1 rounded">
                                  {citizen.digital_id || 'N/A'}
                                </div>
                              </td>

                              {/* Date of Birth */}
                              <td className="border border-gray-600 p-2 text-center">
                                <div className="text-xs text-gray-700">
                                  {citizen.date_of_birth ? new Date(citizen.date_of_birth).toLocaleDateString('en-GB') : 'N/A'}
                                </div>
                              </td>

                              {/* Age */}
                              <td className="border border-gray-600 p-2 text-center">
                                <div className="text-xs font-bold text-purple-700 bg-purple-50 px-2 py-1 rounded">
                                  {calculateAge(citizen.date_of_birth)}
                                </div>
                              </td>

                              {/* Gender */}
                              <td className="border border-gray-600 p-2 text-center">
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-bold text-white shadow-sm ${
                                  citizen.gender === 'male' ? 'bg-gradient-to-r from-blue-500 to-blue-600' : 'bg-gradient-to-r from-pink-500 to-pink-600'
                                }`}>
                                  {citizen.gender === 'male' ? '♂ Male' : '♀ Female'}
                                </span>
                              </td>

                              {/* Phone */}
                              <td className="border border-gray-600 p-2">
                                <div className="text-xs text-gray-700 font-mono">
                                  {citizen.phone || 'N/A'}
                                </div>
                              </td>

                              {/* Registration Date */}
                              <td className="border border-gray-600 p-2 text-center">
                                <div className="text-xs text-green-700 bg-green-50 px-2 py-1 rounded">
                                  {citizen.created_at ? new Date(citizen.created_at).toLocaleDateString('en-GB') : 'N/A'}
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <PeopleIcon className="text-4xl mb-2" />
                    <Typography variant="body1">No citizens registered in this kebele</Typography>
                  </div>
                )}
              </div>
            ))}
          </div>
        ))}

        {/* Footer */}
        <div className="book-footer page-break-before p-8 text-center border-t-2 border-gray-300">
          <Typography variant="h5" className="font-bold mb-4" style={{ color: '#ff8f00' }}>
            {bookData.city_info?.name} City Administration
          </Typography>
          <Typography variant="body1" className="text-gray-600 mb-2">
            Official Citizen Directory
          </Typography>
          <Typography variant="body2" className="text-gray-500">
            Generated on {bookData.city_info?.generated_date} at {bookData.city_info?.generated_time}
          </Typography>
          <Typography variant="caption" className="text-gray-400 mt-4 block">
            This document contains confidential information. Handle with care and in accordance with data protection regulations.
          </Typography>
        </div>
      </div>

      {/* Print Styles */}
      <style>{`
        @media print {
          .no-print {
            display: none !important;
          }

          .page-break-before {
            page-break-before: always;
          }

          .page-break-after {
            page-break-after: always;
          }

          .book-content {
            font-size: 12px;
          }

          .cover-page {
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
          }

          .table-of-contents {
            min-height: 100vh;
          }

          .citizens-table-enhanced {
            margin-top: 16px;
            break-inside: avoid;
            position: relative;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }

          .watermark-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('/images/fasilides-palace.png');
            background-size: 400px 300px;
            background-repeat: repeat;
            background-position: center;
            opacity: 0.08;
            z-index: 1;
            pointer-events: none;
          }

          .citizens-table-enhanced table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
            border: 3px solid #1f2937;
            background: rgba(255, 255, 255, 0.95);
            position: relative;
            z-index: 10;
          }

          .citizens-table-enhanced th {
            background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%) !important;
            border: 2px solid #1f2937;
            padding: 8px 6px;
            font-size: 9px;
            font-weight: bold;
            text-align: center;
            color: #1f2937;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .citizens-table-enhanced td {
            border: 1px solid #4b5563;
            padding: 6px 4px;
            font-size: 8px;
            vertical-align: middle;
            position: relative;
          }

          .citizens-table-enhanced tr {
            break-inside: avoid;
            page-break-inside: avoid;
          }

          .citizens-table-enhanced tr:nth-child(even) {
            background: rgba(255, 247, 237, 0.9) !important;
          }

          .citizens-table-enhanced tr:nth-child(odd) {
            background: rgba(255, 255, 255, 0.9) !important;
          }

          .citizen-photo-enhanced {
            width: 35px;
            height: 35px;
            border: 2px solid #4b5563;
            border-radius: 4px;
            background: #f3f4f6;
            overflow: hidden;
            display: inline-block;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          .citizen-photo-enhanced img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          /* Enhanced styling for data cells */
          .citizens-table-enhanced .font-mono {
            font-family: 'Courier New', monospace;
          }

          .citizens-table-enhanced .bg-blue-50 {
            background-color: rgba(239, 246, 255, 0.8) !important;
          }

          .citizens-table-enhanced .bg-purple-50 {
            background-color: rgba(245, 243, 255, 0.8) !important;
          }

          .citizens-table-enhanced .bg-green-50 {
            background-color: rgba(240, 253, 244, 0.8) !important;
          }

          .citizens-table-enhanced .text-blue-800 {
            color: #1e40af !important;
          }

          .citizens-table-enhanced .text-purple-700 {
            color: #7c3aed !important;
          }

          .citizens-table-enhanced .text-green-700 {
            color: #15803d !important;
          }

          .citizens-table-enhanced .text-orange-600 {
            color: #ea580c !important;
          }

          .subcity-header,
          .kebele-header {
            break-inside: avoid;
          }

          .kebele-section {
            break-inside: avoid;
            page-break-inside: avoid;
          }
        }

        @page {
          margin: 1in;
          size: A4;
        }
      `}</style>
    </div>
  );
};

export default CitizenBookPrintable;
